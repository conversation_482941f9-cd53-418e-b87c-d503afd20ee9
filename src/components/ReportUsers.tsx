import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { Card, CardContent } from "@/modules/shared";
import { User, UserBlockStatus } from "@/modules/users/domain/entities/User";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { MoreVertical, UserPlus } from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

interface ReportUsersProps {
  users: User[] | null;
  isLoading?: boolean;
}
function ReportUsers({ users, isLoading }: ReportUsersProps) {
  const [dropdownUserId, setDropdownUserId] = useState<string | null>(null);
  const [isBlocking, setIsBlocking] = useState(false);
  const navigate = useNavigate();
  const { userInfos } = useAuth();

  // Fermer le dropdown si on clique ailleurs
  React.useEffect(() => {
    const handleClick = () => setDropdownUserId(null);
    if (dropdownUserId) {
      window.addEventListener("click", handleClick);
      return () => window.removeEventListener("click", handleClick);
    }
  }, [dropdownUserId]);

  const avatarGradients = [
    "from-blue-400 via-purple-500 to-pink-500",
    "from-green-400 via-blue-500 to-purple-600",
    "from-pink-400 via-red-500 to-yellow-500",
    "from-indigo-400 via-purple-500 to-pink-500",
    "from-cyan-400 via-blue-500 to-indigo-600",
    "from-orange-400 via-pink-500 to-red-500",
  ];

  const getAvatarGradient = (userId: string) => {
    const index = userId.length % avatarGradients.length;
    return avatarGradients[index];
  };

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent flex items-center space-x-3">
            <UserPlus className="h-6 w-6 text-blue-400" />
            <span>Suggestions de personnes</span>
          </h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <Card
                className="p-6 border-0 shadow-2xl backdrop-blur-xl"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(255,255,255,0.1) 0%, 
                    rgba(255,255,255,0.05) 50%, 
                    rgba(255,255,255,0.1) 100%
                  )`,
                  backdropFilter: "blur(20px)",
                  border: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-24 h-24 bg-white/20 rounded-full"></div>
                  <div className="h-5 bg-white/20 rounded-full w-28"></div>
                  <div className="h-4 bg-white/20 rounded-full w-20"></div>
                  <div className="flex space-x-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const getAge = (birthdate: string): number | null => {
    if (!birthdate) return null;

    const today = new Date();
    const birthDate = new Date(birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return age;
  };

  const handleUpdateBlockStatus = async (
    userId: string,
    blockStatus: UserBlockStatus
  ) => {
    setIsBlocking(true);
    try {
      // Appel API pour mettre à jour le statut de blocage de l'utilisateur
      await userApiRepository.updateBlockStatus(userId, blockStatus);
      console.log(`User ${userId} has been ${blockStatus.toLowerCase()}`);
      setIsBlocking(false);
      toast.success(
        `User ${userId} has been ${blockStatus === UserBlockStatus.BLOCKED ? "blocked" : "unblocked"}`
      );
      window.location.reload();
      
    } catch (error) {
      console.error(`Error updating block status for user ${userId}:`, error);
      setIsBlocking(false);
    }
  };

  if (!users || users.length === 0) {
    return (
      <div className="w-full space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-500 via-blue-400 to-pink-400 bg-clip-text text-transparent flex items-center space-x-3">
            <UserPlus className="h-6 w-6 text-blue-400" />
            <span>No report users for the moment</span>
          </h2>
        </div>
        <Card className="p-8 border-0 shadow-xl backdrop-blur-xl bg-white">
          <div className="text-center text-gray-400">
            No report users for the moment
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
      {users?.map((user) => (
        <div key={user.id} className="w-full">
          <Card
            key={user.id}
            className="group relative bg-white overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 hover:scale-105 cursor-pointer backdrop-blur-xl"
            // style={{
            //   background: `linear-gradient(135deg, 
            //       rgba(255,255,255,0.1) 0%, 
            //       rgba(255,255,255,0.05) 50%, 
            //       rgba(255,255,255,0.1) 100%
            //     )`,
            //   backdropFilter: "blur(20px)",
            //   border: "1px solid rgba(255,255,255,0.1)",
            // }}
            onClick={() => navigate(`/profile/${user.id}`)}
          >
            {/* <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div> */}

            {/* Bordure colorée subtile */}
            {/* <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div> */}

            <CardContent className="p-8 relative z-10">
              <button
                className="absolute -top-2 right-1 z-20 p-2 rounded-full hover:bg-black/20 transition"
                onClick={(e) => {
                  e.stopPropagation();
                  setDropdownUserId(
                    dropdownUserId === user.id ? null : user.id
                  );
                }}
              >
                <MoreVertical className="w-6 h-6 text-black" />
              </button>
              {dropdownUserId === user.id && (
                <div
                  className="absolute right-1 mt-8 w-42 bg-white rounded shadow-lg py-2 z-30"
                  onClick={(e) => e.stopPropagation()}
                >
                  <button
                    disabled={user.blockStatus === UserBlockStatus.BLOCKED}
                    className={`block w-full text-left px-4 py-2 text-green-600 hover:bg-green-50 ${user.blockStatus === UserBlockStatus.BLOCKED ? "opacity-50 cursor-not-allowed" : ""}`}
                    onClick={() => {
                      handleUpdateBlockStatus(user.id, UserBlockStatus.BLOCKED);
                      setDropdownUserId(null);
                    }}
                  >
                    Validate Report
                  </button>
                  <button
                    className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50"
                    onClick={() => {
                      // Annuler le report ici
                      handleUpdateBlockStatus(user.id, UserBlockStatus.NONE);
                      setDropdownUserId(null);
                    }}
                  >
                    Cancel
                  </button>
                </div>
              )}
              <div className="flex justify-center mb-6 mt-4">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-0 group-hover:opacity-50 transition-opacity duration-300 scale-110"></div>

                  {user.profilePhoto ? (
                    <img
                      src={user.profilePhoto}
                      alt={`Photo de profil de ${user.username}`}
                      className={`w-24 h-24 rounded-full object-cover ring-4 ring-white/20 shadow-2xl relative z-10 group-hover:ring-blue-400/40 transition-all duration-300 ${userInfos?.isPaid ? 'opacity-100' : 'opacity-[0.09] grayscale bg-black'}`}
                    />
                  ) : (
                    <div
                      className={`w-24 h-24 rounded-full bg-gradient-to-br ${getAvatarGradient(
                        user.id
                      )} flex items-center justify-center shadow-2xl relative z-10 group-hover:shadow-3xl transition-all duration-300 ring-2 ring-white/20`}
                    >
                      <span className="text-xl font-bold text-white drop-shadow-lg">
                        {getInitials(user.username)}
                      </span>
                    </div>
                  )}

                  {/* Indicateur en ligne avec pulsation */}
                  {user.isOnline && (
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full ring-4 ring-black/20 shadow-lg z-20">
                      <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                      <div className="absolute inset-1 bg-green-400 rounded-full"></div>
                    </div>
                  )}
                </div>
              </div>
              <div className="text-center space-y-3 mb-6">
                <h3 className="text-xl font-bold text-black transition-colors duration-300 drop-shadow-sm">
                  {user.lastName} {user.firstName}
                </h3>

                {user.birthdate && (
                  <p className="text-black/70 text-sm font-medium">
                    {getAge(user.birthdate as string)} years old
                  </p>
                )}

                {user.country && (
                  <p className="text-black/60 text-xs">{user.country}</p>
                )}
              </div>
              <div className="flex items-center justify-end text-white/70">
                {user.blockStatus === UserBlockStatus.BLOCKED ? (
                  <div className="text-red-500 text-sm font-medium capitalize">
                    {UserBlockStatus.BLOCKED}
                  </div>
                ) : (
                  <div className="text-green-500 text-sm font-medium capitalize">
                    {UserBlockStatus.PENDING}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      ))}
    </div>
  );
}

export default ReportUsers;
