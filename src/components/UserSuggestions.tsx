import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, Button } from "@/modules/shared/presentation";
import { useUserSuggestions } from "@/modules/users/presentation/hooks/useUserSuggestions";
import { useUserLikes } from "@/modules/users/presentation/hooks/useUserLikes";
import { User } from "@/modules/users/domain/entities/User";
import {
  MessageCircle,
  UserPlus,
  Heart,
  X,
  RefreshCw,
  Star,
  Loader2,
  AlertCircle,
  EyeIcon,
} from "lucide-react";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { toast } from "react-toastify";

interface UserSuggestionsProps {
  onStartChat: (user: User) => void;
}

export const UserSuggestions: React.FC<UserSuggestionsProps> = ({
  onStartChat,
}) => {
  const navigate = useNavigate();
  const { users, isLoading, error, loadSuggestions, refreshSuggestions } =
    useUserSuggestions(100);
  const { likeUser, dislikeUser, isLiking, isDisliking } = useUserLikes();

  const [usersAffiches, setUsersAffiches] = useState<User[]>([]);

  const [openModalReportUser, setOpenModalReportUser] = React.useState(false);
  const [reportDescription, setReportDescription] = React.useState("");
  const [userReport, setUserreport] = React.useState<User | null>(null);
  const { user, userInfos, loadUserInfos } = useAuth();
  const [isReporting, setIsReporting] = React.useState(false);

  useEffect(() => {
    // Charger les suggestions au montage du composant
    loadSuggestions();
  }, [loadSuggestions ]);

  useEffect(() => {
    // Charger les suggestions au montage du composant
    loadUserInfos();
  }, [loadUserInfos ]);

  useEffect(() => {
    setUsersAffiches(users);
  }, [users]);

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleStartChat = (user: User) => {
    onStartChat(user);
  };

  const handleRefreshSuggestions = async () => {
    await refreshSuggestions();
  };

  const handleUserClick = (user: User) => {
    navigate(`/profile/${user.id}`);
  };

  const handleLikeUser = async (e: React.MouseEvent, user: User) => {
    e.stopPropagation();
    const success = await likeUser(user.id);
    if (!success) {
      
      toast.error("Impossible to like user, verify your credits");
    }else{
      setUsersAffiches((prev) =>
        prev.map((u) => (u.id !== user.id ? u : u.withInteractionType("like")))
      );
    }
  };

  const handleDislikeUser = async (e: React.MouseEvent, user: User) => {
    e.stopPropagation();
    const success = await dislikeUser(user.id);
    if (success) {
      setUsersAffiches((prev) =>
        prev.map((u) =>
          u.id !== user.id ? u : u.withInteractionType("dislike")
        )
      );
    }
  };

  const handleOpenModalReportUser = (user: User) => {
    setOpenModalReportUser(true);
    setUserreport(user);
    // Logique pour ouvrir le modal de signalement d'utilisateur
    console.log(`Open report modal for user: ${user.username}`);
  };

  

  // Report user search modal
  const handleReportUser = async (): Promise<void> => {
    if (!userReport || !user) {
      console.error("User or userReport is not defined");
      return;
    }
    setIsReporting(true);
    try {
      const data = {
        reportedByID: user?.id || "",
        reportedToID: userReport?.id || "",
        reason: reportDescription || "",
      };
      await userApiRepository.reportUser(data);
      setTimeout(() => {
        setOpenModalReportUser(false);
        setReportDescription("");
        setUserreport(null);
        setIsReporting(false);
      }, 5000);
      // Logique pour signaler l'utilisateur
      console.log(`Reporting user: ${userReport?.username}`);
      // Vous pouvez appeler une API ou une fonction pour gérer le signalement ici
    } catch (error) {
      console.error("Erreur lors du signalement de l'utilisateur:", error);
      setIsReporting(false);
    }
  };

  // Couleurs d'avatar aléatoires pour plus de variété
  const avatarGradients = [
    "from-blue-400 via-purple-500 to-pink-500",
    "from-green-400 via-blue-500 to-purple-600",
    "from-pink-400 via-red-500 to-yellow-500",
    "from-indigo-400 via-purple-500 to-pink-500",
    "from-cyan-400 via-blue-500 to-indigo-600",
    "from-orange-400 via-pink-500 to-red-500",
  ];

  const getAvatarGradient = (userId: string) => {
    const index = userId.length % avatarGradients.length;
    return avatarGradients[index];
  };


  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent flex items-center space-x-3">
            <UserPlus className="h-6 w-6 text-pink-400" />
            <span>People suggestions</span>
          </h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <Card
                className="p-6 border-0 shadow-2xl backdrop-blur-xl"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(255,255,255,0.1) 0%, 
                    rgba(255,255,255,0.05) 50%, 
                    rgba(255,255,255,0.1) 100%
                  )`,
                  backdropFilter: "blur(20px)",
                  border: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                <div className="flex flex-col items-center space-y-4">
                  <div className="w-24 h-24 bg-white/20 rounded-full"></div>
                  <div className="h-5 bg-white/20 rounded-full w-28"></div>
                  <div className="h-4 bg-white/20 rounded-full w-20"></div>
                  <div className="flex space-x-3">
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                    <div className="w-10 h-10 bg-white/20 rounded-full"></div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-pink-400 via-blue-100 to-purple-500 bg-clip-text text-transparent flex items-center space-x-3">
            <UserPlus className="h-6 w-6 text-purple-400" />
            <span>People suggestions</span>
          </h2>
        </div>
        <Card
          className="p-8 border-0 shadow-2xl backdrop-blur-xl"
          style={{
            background: `linear-gradient(135deg, 
        rgba(239, 68, 68, 0.1) 0%, 
        rgba(220, 38, 127, 0.05) 50%, 
        rgba(239, 68, 68, 0.1) 100%
      )`,
            backdropFilter: "blur(20px)",
            border: "1px solid rgba(239, 68, 68, 0.2)",
          }}
        >
          <div className="text-center">
            <div className="text-red-400 mb-6 text-lg font-medium">
              Error loading: {error}
            </div>
            <Button
              variant="outline"
              onClick={handleRefreshSuggestions}
              className="flex items-center space-x-2 bg-white/10 hover:bg-red-500/20 border-red-400/40 text-red-300 hover:text-red-200 hover:border-red-300 backdrop-blur-sm"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Try again</span>
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <div className="block lg:flex items-center justify-between">
        <h2 className="text-2xl mb-6 lg:mb-0 font-bold bg-gradient-to-r from-pink-500 via-blue-500 to-purple-500 bg-clip-text text-transparent flex items-center space-x-3">
          <UserPlus className="h-6 w-6 text-blue-400" />
          <span>People suggestions</span>
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshSuggestions}
          disabled={isLoading}
          className="flex items-center space-x-2 bg-black/10 hover:bg-black/20 border-black/20 hover:border-black/30 text-black/90 hover:text-black backdrop-blur-sm transition-all duration-300"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Refresh</span>
        </Button>
      </div>

      {usersAffiches.length === 0 ? (
        <Card
          className="p-12 border-0 shadow-2xl backdrop-blur-xl"
          style={{
            background: `linear-gradient(135deg, 
              rgba(59, 130, 246, 0.1) 0%, 
              rgba(147, 51, 234, 0.05) 25%,
              rgba(236, 72, 153, 0.05) 75%,
              rgba(59, 130, 246, 0.1) 100%
            )`,
            backdropFilter: "blur(20px)",
            border: "1px solid rgba(59, 130, 246, 0.2)",
          }}
        >
          <div className="text-center text-white/80">
            <div className="relative mb-6">
              <UserPlus className="h-16 w-16 mx-auto text-black/30" />
              <Star className="h-6 w-6 absolute -top-2 -right-2 text-yellow-400 animate-pulse" />
            </div>
            <p className="text-xl font-semibold mb-3 text-black">
              No suggestions available
            </p>
            <p className="text-sm mb-6 text-black/70">
              Check back later to discover amazing new people
            </p>
            <Button
              variant="outline"
              onClick={handleRefreshSuggestions}
              className="flex items-center space-x-2 mx-auto bg-white/10 hover:bg-black/20 border-black/20 hover:border-black/30 text-white/90 hover:text-black backdrop-blur-sm"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Load suggestions</span>
            </Button>
          </div>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {usersAffiches.map((user) => (
            <Card
              key={user.id}
              className="group relative overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-2 hover:scale-105 cursor-pointer backdrop-blur-xl"
              // style={{
              //   background: `linear-gradient(135deg,
              //     rgba(255,255,255,0.1) 0%,
              //     rgba(255,255,255,0.05) 50%,
              //     rgba(255,255,255,0.1) 100%
              //   )`,
              //   backdropFilter: "blur(20px)",
              //   border: "1px solid rgba(255,255,255,0.1)",
              // }}
              onClick={() => handleUserClick(user)}
            >
              {/* Effet de brillance au survol */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

              {/* Bordure colorée subtile */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>

              <CardContent className="p-8 relative z-10">
                {/* Photo de profil avec effet de halo */}
                <div className="flex justify-center mb-6 mt-4">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-purple-500 rounded-full blur-md opacity-0 group-hover:opacity-50 transition-opacity duration-300 scale-110"></div>

                    {user.profilePhoto ? (
                      <img
                        src={user.profilePhoto}
                        alt={`Pictire of ${user.username}`}
                        className={`w-24 h-24 rounded-full object-cover ring-4 ring-white/20 shadow-2xl relative z-10 group-hover:ring-purple-400/40 transition-all duration-300 ${
                          user?.isDeflouted
                            ? "opacity-100"
                            : "opacity-[0.09] grayscale bg-black"
                        }`}
                      />
                    ) : (
                      <div
                        className={`w-24 h-24 rounded-full bg-gradient-to-br ${getAvatarGradient(
                          user.id
                        )} flex items-center justify-center shadow-2xl relative z-10 group-hover:shadow-3xl transition-all duration-300 ring-2 ring-white/20`}
                      >
                        <span className="text-xl font-bold text-black drop-shadow-lg">
                          {getInitials(user.username)}
                        </span>
                      </div>
                    )}

                    {/* Indicateur en ligne avec pulsation */}
                    {user.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full ring-4 ring-black/20 shadow-lg z-20">
                        <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                        <div className="absolute inset-1 bg-green-400 rounded-full"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Informations utilisateur */}
                <div className="text-center space-y-3 mb-6">
                  <h3 className="text-xl font-bold text-black group-hover:text-pink-500 transition-colors duration-300 drop-shadow-sm">
                    {/* {user.lastName} {user.firstName} */}
                    {user.username}
                  </h3>

                  {user.getAge() && (
                    <p className="text-black/70 text-sm font-medium">
                      {user.getAge()} years old
                    </p>
                  )}

                  {user.country && (
                    <p className="text-black/60 text-xs">{user.country}</p>
                  )}
                </div>

                {/* Boutons d'action avec effets améliorés */}
                <div className="flex justify-center space-x-4">
                  {user.interactionType === "NONE" && (
                    <>
                      <Button
                        variant="outline"
                        size="sm"
                        disabled={isDisliking === user.id}
                        className="w-12 h-12 rounded-full p-0 border-2 border-black/20 hover:border-red-400 hover:bg-red-500/80 hover:text-white group/btn transition-all duration-300 hover:scale-110 hover:shadow-lg backdrop-blur-sm"
                        onClick={(e) => handleDislikeUser(e, user)}
                      >
                        {isDisliking === user.id ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <X className="h-5 w-5 text-black/80 group-hover/btn:text-white transition-colors duration-300" />
                        )}
                      </Button>
                      
                      {userInfos?.canLike ? (
                        <Button
                          size="sm"
                          disabled={isLiking === user.id}
                          className="w-12 h-12 rounded-full p-0 bg-gradient-to-r from-pink-500 via-rose-500 to-red-500 hover:from-pink-400 hover:via-rose-400 hover:to-red-400 shadow-2xl hover:shadow-pink-500/25 transition-all duration-300 hover:scale-110 relative overflow-hidden"
                          onClick={(e) => handleLikeUser(e, user)}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
                          {isLiking === user.id ? (
                            <Loader2 className="h-5 w-5 animate-spin relative z-10" />
                          ) : (
                            <Heart className="h-5 w-5 text-white relative z-10 drop-shadow-sm" />
                          )}
                        </Button>
                      ):null}
                    </>
                  )}
                  {userInfos?.canSendMessage ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-12 h-12 rounded-full p-0 border-2 border-black/20 hover:border-blue-400 hover:bg-blue-500/80 hover:text-white group/btn transition-all duration-300 hover:scale-110 hover:shadow-lg backdrop-blur-sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleStartChat(user);
                      }}
                    >
                      <MessageCircle className="h-5 w-5 text-black/80 group-hover/btn:text-white transition-colors duration-300" />
                    </Button>
                  ):null}
                  {/* {
                    userInfos?.totalCredit &&
                    userInfos?.totalCredit >= 5 ? (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-12 h-12 rounded-full p-0 border-2 border-black/20 hover:border-blue-400 hover:bg-blue-500/80 hover:text-white group/btn transition-all duration-300 hover:scale-110 hover:shadow-lg backdrop-blur-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeflouteUserPhoto(e, user.id);
                        }}
                      >
                        <EyeIcon className="h-5 w-5 text-black/80 group-hover/btn:text-white transition-colors duration-300" />
                      </Button>
                    ):null
                  } */}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 rounded-xl w-full flex items-center justify-center gap-x-4 mt-6 p-0 border border-black/20 hover:border-red-400 hover:bg-red-500/80"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenModalReportUser(user);
                  }}
                >
                  <AlertCircle className="h-3 w-3 text-black/80" />
                  <span className="text-xs text-black/80">Report user</span>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      {openModalReportUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white/100 p-8 rounded-2xl shadow-2xl w-full max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-gray-900">
              Report User
            </h2>
            <label
              className="block mb-2 text-gray-700 font-medium"
              htmlFor="report-description"
            >
              Please describe the reason for reporting:
            </label>
            <textarea
              id="report-description"
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400 text-gray-900 bg-white mb-6"
              rows={4}
              placeholder="Describe the issue..."
              value={reportDescription}
              onChange={(e) => setReportDescription(e.target.value)}
            />
            <div className="mt-4 flex justify-end space-x-2">
              <button
                className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                onClick={() => setOpenModalReportUser(false)}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-60"
                disabled={isReporting}
                onClick={handleReportUser}
              >
                {isReporting ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  "Report"
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
