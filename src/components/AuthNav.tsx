import { Heart } from "lucide-react";
import React from "react";
import { useNavigate } from "react-router-dom";

function AuthNav() {
  const navigate = useNavigate();
  return (
    <nav className="relative w-full z-50 flex shadow-lg p-0 m-0 rounded-none">
      {/* Partie gauche : logo + nom sur fond blanc */}
      <div className="flex items-center px-4 bg-white">
        <div onClick={() => navigate("/splash")} className="flex items-center space-x-2">
          <div className="w-8 h-8 border border-pink-500 rounded-full flex items-center justify-center">
            <Heart className="text-pink-500" size={16} />
          </div>
          <span className="text-xl font-bold text-purple-700">TooDiscreet</span>
        </div>
      </div>

      {/* Partie qui prend l'espace vide */}
      <div className="flex-1 bg-white"></div>

      {/* Partie droite : boutons sur fond rose */}
      <div className="flex w-1/2 sm:w-1/3 md:w-1/4 flex justify-end items-center space-x-8 h-16 px-4 bg-pink-500">
        <button
          onClick={() => navigate("/login")}
          className="text-white px-4 py-2 rounded-full hover:bg-pink-600 hover:shadow-lg"
        >
          Login
        </button>
        <button>
          <div
            onClick={() => navigate("/signup")}
            className="bg-white text-pink-500 rounded-full px-4 py-2 hover:bg-pink-600 hover:text-white hover:shadow-lg"
          >
            Register
          </div>
        </button>
      </div>
    </nav>
  );
}

export default AuthNav;
