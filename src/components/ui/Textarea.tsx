// Textarea.tsx
import { useThemeClasses } from '@/modules/shared';
import React from 'react';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(({
  label,
  error,
  helperText,
  className = '',
  id,
  ...props
}, ref) => {
  const themeClasses = useThemeClasses();
  const textareaId = id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

  const baseTextareaClasses = 'w-full px-4 py-3 text-sm border rounded-lg transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed text-neutral-900 bg-white';
  
  const textareaClasses = error
    ? 'border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-opacity-20'
    : `border-neutral-300 ${themeClasses.focusPrimary} focus:border-[var(--color-primary-500)]`;

  return (
    <div className="space-y-2 w-full">
      {label && (
        <label 
          htmlFor={textareaId}
          className={`block text-sm font-medium ${themeClasses.textNeutral}`}
        >
          {label}
          {props.required && <span className="text-black ml-1">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        id={textareaId}
        className={`
          ${baseTextareaClasses}
          ${textareaClasses}
          ${className}
        `}
        {...props}
      />
      
      {(error || helperText) && (
        <p className={`text-xs ${error ? 'text-red-900' : 'text-white-500'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';