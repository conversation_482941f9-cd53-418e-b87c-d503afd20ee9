import { User } from "@/modules/users/domain/entities/User";
import { useEffect, useState } from "react";
import { Input, Select, useThemeClasses } from "@/modules/shared/presentation";
import { Country } from "@/modules/users/domain/valueobjects/Country";
import { Gender } from "@/modules/users/domain/valueobjects/PersonalInfo";
import { Appearance } from "@/modules/users/domain/valueobjects/Appearance";
import PhotosStep from "@/modules/users/presentation/components/onboarding/PhotosStep";
import { OnboardingData } from "@/modules/users/application/usecases/CompleteOnboardingUseCase";
import { UserIcon, Users } from "lucide-react";
import UploadPhoto from "./UploadPhoto";
import { OnboardingPhoto } from "@/modules/users/domain/valueobjects/OnboardingPhoto";
import { usePhotoUpload } from "@/modules/users/presentation/hooks/usePhotoUpload";
import { EditProfileButtons } from "./EditProfileButtons";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { useNavigate } from "react-router-dom";

interface EditProfileProps {
  user: User | null;
  userId: string | undefined;
}

export const EditProfile: React.FC<EditProfileProps> = ({ user, userId }) => {
  const themeClasses = useThemeClasses();

  const [formData, setFormData] = useState({
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    email: user?.email || "",
    birthdate: user?.birthdate ? user.birthdate.split("T")[0] : "", // Format pour input date
    gender: user?.gender || Gender.MALE, // Valeur par défaut
    country: user?.country || "",
    ethnicity: user?.ethnicity || "",
    height: user?.height || "",
    profilePhoto: user?.profilePhoto || "",
    hairColor: user?.hairColor || "",
    eyeColor: user?.eyeColor || "",
    bodyType: user?.bodyType || "",
    bodyColor: user?.bodyColor || "",
    religion: user?.religion || "",
  });

  const countries = Country.getAllSupported();
  const [errors, setErrors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<"personal" | "appearance">(
    "personal"
  );
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.email || "",
        birthdate: user.birthdate ? user.birthdate.split("T")[0] : "",
        gender: user.gender || Gender.MALE,
        country: user.country || "FR",
        ethnicity: user.ethnicity || "",
        height: user?.height || 0,
        profilePhoto: user.profilePhoto || "",
        hairColor: user.hairColor || "",
        eyeColor: user.eyeColor || "",
        bodyType: user.bodyType || "",
        bodyColor: user.bodyColor || "",
        religion: user.religion || "",
      });
    }
  }, [user]);

  const {
    uploadPhoto,
    isUploading,
    error: uploadError,
    clearError,
  } = usePhotoUpload();

  const data: any = {
    url: formData.profilePhoto,
  };

  const [photo, setPhoto] = useState<OnboardingPhoto | undefined>(data);
  const allErrors = [...errors, ...(uploadError ? [uploadError] : [])];

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    if (errors.length > 0) {
      setErrors([]);
    }
  };

  const formatHeight = (heightCm: number | string) => {
    if (!heightCm) return "";
    const height = typeof heightCm === "string" ? parseInt(heightCm) : heightCm;
    if (isNaN(height)) return "";
    const meters = Math.floor(height / 100);
    const centimeters = height % 100;
    return `${meters}m${centimeters.toString().padStart(2, "0")}`;
  };

  const getAge = () => {
    if (!formData.birthdate) return null;
    const today = new Date();
    const birthDate = new Date(formData.birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      if (!photo) {
        setErrors(["Profile photo is required"]);
        return;
      }
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const handlePhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setErrors([]);
    clearError();

    try {
      // Upload via le hook qui utilise l'architecture clean
      const uploadResult = await uploadPhoto(file);

      const uploadedPhoto = OnboardingPhoto.fromUpload({
        id: uploadResult.id,
        url: uploadResult.url,
        name: file.name,
      });

      setPhoto(uploadedPhoto);
    } catch (error) {
      if (error instanceof Error) {
        setErrors([error.message]);
      }
    }
  };

  const removePhoto = () => {
    setPhoto(undefined);
    setErrors([]);
    clearError();
  };

  const handleSave = async () => {
    // Préparation des données avec des vérifications plus robustes
    const onboardingData: any = {
      firstName: formData.firstName || user?.firstName || undefined,
      lastName: formData.lastName || user?.lastName || undefined,
      email: formData.email || user?.email || undefined,
      gender: formData.gender || user?.gender || undefined,
      country: formData.country || user?.country || undefined,
      ethnicity: formData.ethnicity || user?.ethnicity || undefined,
      height: Number(formData.height) || user?.height || undefined,
      profilePhoto: photo?.url || user?.profilePhoto || undefined,
      hairColor: formData.hairColor || user?.hairColor || undefined,
      eyeColor: formData.eyeColor || user?.eyeColor || undefined,
      bodyType: formData.bodyType || user?.bodyType || undefined,
      bodyColor: formData.bodyColor || user?.bodyColor || undefined,
      religion: formData.religion || user?.religion || undefined,
    };

    // Gestion spécifique de birthdate
    if (formData.birthdate) {
      onboardingData.birthdate = new Date(formData.birthdate);
    } else if (user?.birthdate) {
      onboardingData.birthdate = new Date(user.birthdate);
    } else {
      onboardingData.birthdate = null; // ou undefined selon les besoins de l'API
    }

    setIsSaving(true);

    try {
      if (!userId) {
        throw new Error("User ID is missing");
      }

      const response = await userApiRepository.updateConnectedUserProfile(
        userId,
        onboardingData
      );

      setSuccessMessage("Profile updated successfully");
      setErrors([]);
      setIsSaving(false);

      // Gestion des succès (redirection, notification, etc.)
      navigate("/connected-user-profile");
      // ...
    } catch (error: unknown) {
      console.error("Error updating profile:", error);
      setIsSaving(false);

      if (error instanceof Error) {
        setErrors([error.message]);
        setSuccessMessage(null);
      } else {
        setErrors(["An unknown error occurred"]);
      }
    }
  };

  return (
    <div className="w-full lg:w-[80%] mx-auto mt-6">
      <UploadPhoto
        user={user}
        handleSubmit={handleSubmit}
        handlePhotoUpload={handlePhotoUpload}
        photo={photo}
        removePhoto={removePhoto}
        allErrors={allErrors}
        isUploading={isUploading}
        themeClasses={themeClasses}
        successMessage={successMessage}
      />
      <div className="my-6">
          <div className="flex space-x-1 bg-white/10 backdrop-blur-xl rounded-lg p-1 border border-white/20">
            <button
              onClick={() => setActiveTab("personal")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "personal"
                  ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <UserIcon className="h-4 w-4" />
              <span>Personal Information</span>
            </button>
            <button
              onClick={() => setActiveTab("appearance")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "appearance"
                  ? "bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <Users className="h-4 w-4" />
              <span>Appearance</span>
            </button>
          </div>
        </div>

      <div className=" bg-white backdrop-blur-xl rounded-lg p-6 border border-white/20 md:grid-cols-2 gap-8 w-full items-start">
        {activeTab === "personal" && (
          <div className="flex w-full flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
            <div className="flex-1 space-y-6">
              {/* <div className="hidden">
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  FirstName
                </label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <Input
                      value={formData.firstName}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleInputChange("firstName", e.target.value)
                      }
                      placeholder="Your first name"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="hidden">
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  LastName
                </label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <Input
                      value={formData.lastName}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleInputChange("lastName", e.target.value)
                      }
                      placeholder="Your last name"
                      required
                    />
                  </div>
                </div>
              </div> */}

              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  Email Address
                </label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <Input
                      value={formData.email}
                      onChange={
                        (e: React.ChangeEvent<HTMLInputElement>) =>
                          handleInputChange("email", e.target.value) // Corrigé: était "firstName"
                      }
                      placeholder="Your email address"
                      required
                    />
                  </div>
                </div>
              </div>
              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  Birth Date
                </label>
                <div className="flex items-center space-x-3">
                  <div className="flex-1">
                    <Input
                      className="flex-1 text-gray-500"
                      type="date"
                      value={formData.birthdate}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        handleInputChange("birthdate", e.target.value)
                      }
                      max={
                        new Date(Date.now() - 18 * 365.25 * 24 * 60 * 60 * 1000)
                          .toISOString()
                          .split("T")[0]
                      }
                      required
                    />
                  </div>
                  <div
                    className={`text-sm font-medium ${themeClasses.textNeutral} whitespace-nowrap min-w-16`}
                  >
                    {getAge() && (
                      <div
                        className={`text-xs w-[40px] ${themeClasses.textPinkLight}`}
                      >
                        {getAge()} yarns old
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex-1 space-y-6">
              
              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  Genre
                </label>
                <Select
                  value={formData.gender}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleInputChange("gender", e.target.value)
                  }
                >
                  <option value={Gender.MALE}>Male</option>
                  <option value={Gender.FEMALE}>Female</option>
                  <option value={Gender.NON_BINARY}>Non-binary</option>
                  <option value={Gender.OTHER}>Other</option>
                </Select>
              </div>

              <div>
                <label
                  className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
                >
                  Country
                </label>
                <Select
                  value={formData.country}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    handleInputChange("country", e.target.value)
                  }
                >
                  <option value="">Select a country</option>
                  {countries.map((country) => (
                    <option key={country.code} value={country.code}>
                      {country.name}
                    </option>
                  ))}
                </Select>
              </div>
            </div>
          </div>
        )}

        {/* Colonne droite */}
        {activeTab === "appearance" && (
          <div className="space-y-6">
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Ethnicity
              </label>
              <Select
                value={formData.ethnicity}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("ethnicity", e.target.value)
                }
              >
                <option value="">Sélectionnez une origine</option>
                {Appearance.getEthnicityOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>

            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Height
              </label>
              <div className="flex items-center space-x-3">
                <div className="flex-1">
                  <Input
                    type="number"
                    min="120"
                    max="250"
                    value={formData.height}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleInputChange("height", e.target.value)
                    }
                    placeholder="170"
                  />
                </div>
                <div
                  className={`text-sm font-medium ${themeClasses.textNeutral} whitespace-nowrap min-w-16`}
                >
                  {formData.height ? (
                    <span
                      className={`${themeClasses.textPrimary} font-semibold`}
                    >
                      {formatHeight(formData.height)}
                    </span>
                  ) : (
                    <span className="text-neutral-400">cm</span>
                  )}
                </div>
              </div>
            </div>
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Hair color
              </label>
              <Select
                value={formData.hairColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("hairColor", e.target.value)
                }
              >
                <option value="">Sélect hair color</option>
                {Appearance.getHairColorOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Eye color
              </label>
              <Select
                value={formData.eyeColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("eyeColor", e.target.value)
                }
              >
                <option value="">Sélect eye color</option>
                {Appearance.getEyeColorOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Body type
              </label>
              <Select
                value={formData.bodyType}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("bodyType", e.target.value)
                }
              >
                <option value="">Sélect body type</option>
                {Appearance.getBodyTypeOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Body color
              </label>
              <Select
                value={formData.bodyColor}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("bodyColor", e.target.value)
                }
              >
                <option value="">Sélect body color</option>
                {Appearance.getBodyColorOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
            <div>
              <label
                className={`block text-sm font-medium ${themeClasses.textPrimaryDark} mb-2`}
              >
                Religion
              </label>
              <Select
                value={formData.religion}
                onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                  handleInputChange("religion", e.target.value)
                }
              >
                <option value="">Sélect religion</option>
                {Appearance.getReligionOptions().map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </Select>
            </div>
          </div>
        )}
      </div>
      <div>
        <EditProfileButtons
          handleSave={handleSave}
          isUploading={isUploading}
          themeClasses={themeClasses}
          isLoading={isSaving}
        />
      </div>
    </div>
  );
};
