import { PacksRepository } from "../../domain/repositories/PacksRepository";
import packApiClient from "./PackApiClient";

export class PackApiRepository implements PacksRepository {
  async createPack(data: any): Promise<any> {
    console.log("createPack data:", data);
    try {
      const response = await packApiClient.createPack(data);
      return response;
    } catch (error) {
      console.error("Failed to create pack:", error);
      throw new Error(
        `Failed to create pack: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }
}

export const packApiRepository = new PackApiRepository();
export default packApiRepository;
