import { apiService } from "@/modules/shared";
import { ApiResponse, CreatePackResponse } from "./types/ApiResponseTypes";

export class PackApiClient {
  constructor() {
    // Utilise maintenant le service API partagé qui gère déjà l'authentification
  }

  public async createPack(data: any): Promise<ApiResponse<CreatePackResponse>> {
    try {
      return await apiService.post<ApiResponse<CreatePackResponse>>(
        `/api/packs`,
        data
      );
    } catch (error) {
      console.error("Error creating pack:", error);
      throw this.handleError(error);
    }
  }

  private handleError(error: any): Error {
    const message = error.response?.data?.message || error.message;
    const status = error.response?.status;

    switch (status) {
      case 401:
        return new Error("Unauthorized - Please login again");
      case 403:
        return new Error(
          "Forbidden - You do not have permission to perform this action"
        );
      case 404:
        return new Error("User not found");
      case 500:
        return new Error("Internal Server Error - Please try again later");
      default:
        return new Error(message || "An unexpected error occurred");
    }
  }
}  

export const packApiClient = new PackApiClient();
export default packApiClient;
