export class Packs {
  constructor(
    public readonly id: string,
    public readonly user_id: string,
    public readonly pack_name: string,
    public readonly description: string,
    public readonly price: number,
    public readonly credit_value: number,
    public readonly expiry_date: Date,
    public readonly like_count: number,
    public readonly message_count: number,
    public readonly deflouted_image_count: number,
    public readonly remaining_likes: number,
    public readonly remaining_deflouted_images: number,
    public readonly remaining_messages: number,
    public readonly created_at: Date,
    public readonly updated_at: Date,
  ) {}

  static create(data: any): Packs {
    return new Packs(
      data.id,
      data.user_id,
      data.pack_name,
      data.description,
      data.price,
      data.credit_value,
      data.expiry_date,
      data.like_count,
      data.message_count,
      data.deflouted_image_count,
      data.remaining_likes,
      data.remaining_deflouted_images,
      data.remaining_messages,
      data.created_at,
      data.updated_at,
    );
  }
}