import { Email } from "@/modules/auth/domain/valueobjects/Email";
import { Password } from "../valueobjects/Password";
import { Username } from "../valueobjects/Username";
import { ContactUserData } from "../entities/AuthUser";

export interface AuthUserRepository {
    login(email: Email, password: Password): Promise<string | null>;
    createUser(email: Email, password: Password, username: Userna<PERSON>): Promise<void>;
    forgotPassword(email: Email): Promise<void>;
    resetPassword(token: string, password: Password): Promise<void>;
    checkResetPasswordToken(token: string): Promise<boolean>;
    contactUs(formData: ContactUserData): Promise<void>;
}