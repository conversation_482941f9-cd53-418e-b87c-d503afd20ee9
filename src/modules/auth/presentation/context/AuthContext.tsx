import React, { create<PERSON>onte<PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useState } from "react";
import { Auth<PERSON><PERSON>, ContactUserData } from "../../domain/entities/AuthUser";
import { Login } from "../../application/usecases/Login";
import { AuthUserApiRepository } from "../../infrastructure/api/AuthUserApiRepository";
import { Password } from "../../domain/valueobjects/Password";
import { Email } from "../../domain/valueobjects/Email";
import { Username } from "../../domain/valueobjects/Username";
import { SignUp } from "../../application/usecases/SignUp";
import { ForgotPassword } from "../../application/usecases/ForgotPassword";
import { ResetPassword } from "../../application/usecases/ResetPassword";
import { useAppWebSocket } from "../../../shared/presentation/hooks/useAppWebSocket";
import { multiWebSocketService } from "../../../shared/infrastructure/services/MultiWebSocketService";
import { Api } from "@/modules/shared/domain/repositories/Api";
import { toast } from "react-toastify";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import { User } from "@/modules/users/domain/entities/User";

export interface AuthContextType {
  isAuthenticated: boolean;
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, username: string) => Promise<void>;
  logout: () => void;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  checkResetPasswordToken: (token: string) => Promise<boolean>;
  clearError: () => void;
  contactUs: (formData: ContactUserData) => Promise<void>;
  sendingEmail: boolean;
  userInfos: User | null;
  loadUserInfos: () => Promise<User | null>;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

interface AuthProviderProps {
  children: React.ReactNode;
  apiInstance: Api;
}

export const AuthProvider = ({ children, apiInstance }: AuthProviderProps) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sendingEmail, setSendingEmail] = useState(false);
  const [userInfos, setUserInfos] = useState<User | null>(null);

  const authRepository = new AuthUserApiRepository(apiInstance);
  const { connectAppWebSocket, disconnectAppWebSocket } = useAppWebSocket();

  const clearError = () => setError(null);
  console.log("IS AUTHENTICATED", isAuthenticated);
  

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await authRepository.getUserProfile();
      if (profile) {
        setUser({
          id: profile.userId,
          email: new Email(profile.email),
          username: new Username(profile.username),
          roles: profile.roles,
        });
        setIsAuthenticated(true);
        setLoading(false);

        // Connecter automatiquement au WebSocket global pour analytics
        const token = localStorage.getItem("token");
        if (token) {
          try {
            await connectAppWebSocket(token, {
              onConnect: () => {
                // Connected to global analytics WebSocket
              },
              onDisconnect: () => {
                // Disconnected from global analytics WebSocket
              },
              onUserStatus: (data) => {
                // Ici on pourrait dispatcher des événements pour mettre à jour l'UI
              },
              onError: (error) => {
                // Global WebSocket error
              },
            });
          } catch (error) {
            // Ne pas bloquer l'authentification si le WebSocket échoue
          }
        }
      } else {
        localStorage.removeItem("token");
        setIsAuthenticated(false);
        setUser(null);
        disconnectAppWebSocket();
        setLoading(false);
      }
    } catch (error) {
      localStorage.removeItem("token");
      setIsAuthenticated(false);
      setUser(null);
      disconnectAppWebSocket();
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await new Login(authRepository).execute(email, password);
      localStorage.setItem("token", response.token);

      await loadUserProfile();
    } catch (error: any) {
      setError(error.message || "Login error");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const loadUserInfos = useCallback(() => {
    return userApiRepository
      .getConnectedUserProfile()
      .then((profile) => {
        if (profile) {
          setUserInfos(profile);
          setIsAuthenticated(true);
        } else {
          localStorage.removeItem("token");
          setIsAuthenticated(false);
          setUser(null);
          disconnectAppWebSocket();
        }
        setLoading(false);
        return profile;
      })
      .catch((error) => {
        console.error("Error fetching user profile:", error);
        return null;
      });
  }, [setUserInfos, setIsAuthenticated, setUser, disconnectAppWebSocket]);

  const logout = () => {
    // Émettre l'événement pour notifier le logout
    window.dispatchEvent(new CustomEvent("auth:logout"));

    // Déconnecter TOUS les WebSockets de manière coordonnée
    // Cela déconnecte d'abord chat puis app, dans le bon ordre
    multiWebSocketService.disconnectAll();

    localStorage.removeItem("token");
    setIsAuthenticated(false);
    setUser(null);
    setError(null);
  };

  const signup = async (email: string, password: string, username: string) => {
    setLoading(true);
    setError(null);
    try {
      await new SignUp(authRepository).execute(email, password, username);

      await login(email, password);
    } catch (error: any) {
      setError(error.message || "Registration error");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const forgotPassword = async (email: string) => {
    setLoading(true);
    setError(null);
    try {
      await new ForgotPassword(authRepository).execute(new Email(email));
    } catch (error: any) {
      setError(error.message || "Password reset request error");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (token: string, password: string) => {
    setLoading(true);
    setError(null);
    try {
      await new ResetPassword(authRepository, token).execute(
        new Password(password)
      );
      await loadUserProfile();
      // Rediriger vers la page de profil ou une autre page appropriée
      window.location.href = "/login";
    } catch (error: any) {
      setError(error.message || "Password reset error");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const checkResetPasswordToken = async (token: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    try {
      return await authRepository.checkResetPasswordToken(token);
    } catch (error: any) {
      setError(error.message || "Token verification error");
      return false;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem("token");
      if (token) {
        await loadUserProfile();
      }
      setLoading(false);
    };

    initializeAuth();

    // Cleanup function pour déconnecter lors du démontage
    return () => {
      disconnectAppWebSocket();
    };
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      loadUserInfos();
    }
  }, [isAuthenticated, loadUserInfos]);

  const contactUs = async (formData: ContactUserData) => {
    setSendingEmail(true);
    setError(null);
    try {
      await authRepository.contactUs(formData);
    } catch (error: any) {
      setError(error.message || "Contact error");
      toast.error(
        "An error occurred while sending your message. Please try again later."
      );
      throw error;
    } finally {
      setSendingEmail(false);
      toast.success(
        "Your message has been sent. We will get back to you as soon as possible."
      );
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        loading,
        error,
        login,
        logout,
        signup,
        forgotPassword,
        resetPassword,
        checkResetPasswordToken,
        clearError,
        contactUs,
        sendingEmail,
        userInfos,
        loadUserInfos,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
