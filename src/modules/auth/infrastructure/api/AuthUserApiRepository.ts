import { ContactUserData } from "../../domain/entities/AuthUser";
import { AuthUserRepository } from "../../domain/repositories/AuthUserRepository";
import { Email } from "../../domain/valueobjects/Email";
import { Password } from "../../domain/valueobjects/Password";
import { Username } from "../../domain/valueobjects/Username";
import { ApiResponse, LoginResponse, CheckResetTokenResponse, ProfileResponse } from "./types/ApiResponseTypes";
import { Api } from "@/modules/shared/domain/repositories/Api";

export class AuthUserApiRepository implements AuthUserRepository {
  constructor(private readonly api: Api) {}

  async createUser(email: Email, password: Password, username: Username): Promise<void> {
    await this.api.post<ApiResponse>("/api/auth/signup", { 
      email: email.getValue(), 
      password: password.getValue(),
      username: username.getValue()
    });
  }

  async login(email: Email, password: Password): Promise<string | null> {
    const response = await this.api.post<ApiResponse<LoginResponse>>("/api/auth/login", {
      email: email.getValue(),
      password: password.getValue(),
    });
    return response.success ? response.data!.token : null;
  }

  async getUserProfile(): Promise<ProfileResponse | null> {
    try {
      const response = await this.api.get<ApiResponse<ProfileResponse>>("/api/auth/profile");
      return response.success ? response.data! : null;
    } catch (error) {
      console.error('Failed to get user profile:', error);
      return null;
    }
  }

  async forgotPassword(email: Email): Promise<void> {
    await this.api.post<ApiResponse>("/api/auth/forgot-password", {
      email: email.getValue(),
    });
  }

  async resetPassword(token: string, password: Password): Promise<void> {
    await this.api.post<ApiResponse>("/api/auth/reset-password", { 
      token, 
      password: password.getValue()
    });
  }

  async checkResetPasswordToken(token: string): Promise<boolean> {
    const response = await this.api.post<ApiResponse<CheckResetTokenResponse>>(
      "/api/auth/check-reset-password-token",
      { token }
    );
    return response.success ? response.data!.isValid : false;
  }

  async contactUs(formData: ContactUserData): Promise<void> {
    await this.api.post<ApiResponse>("/api/auth/contact", formData);
  }
}
