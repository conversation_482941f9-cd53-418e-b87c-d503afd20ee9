import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { Navigate } from "react-router-dom";

export const AdminRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth(); // Ajoutez isLoading depuis votre hook useAuth
  
  if (loading) {
    return <div>Loading...</div>; // Ou un spinner de chargement
  }

  if (!user || !user?.roles?.includes("admin")) {
    return <Navigate to="/unauthorized" replace />;
  }
  
  return <>{children}</>;
};