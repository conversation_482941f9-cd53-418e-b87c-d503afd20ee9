import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useOnboarding } from '../../../users/presentation/hooks/useOnboarding';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * Composant de garde qui redirige automatiquement vers l'onboarding
 * si l'utilisateur connecté n'a pas terminé son onboarding
 */
export const OnboardingGuard: React.FC<OnboardingGuardProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { 
    shouldRedirectToOnboarding, 
    loading, 
    isAuthenticated,
    getCurrentStep,
    onboardingData
  } = useOnboarding();

  useEffect(() => {
    // Ne pas rediriger si :
    // - Encore en cours de chargement
    // - Utilisateur non authentifié
    // - Déjà sur la page d'onboarding
    // - Sur les pages d'authentification
    // - Les données d'onboarding ne sont pas encore chargées
    if (loading || !isAuthenticated) return;
    
    const authPaths = ['/splash','/login', '/signup', '/forgot-password', '/reset-password','/contacts'];
    const isOnAuthPage = authPaths.some(path => location.pathname.startsWith(path));
    const isOnOnboardingPage = location.pathname === '/onboarding';
    
    if (isOnAuthPage) return;

    // Attendre que les données d'onboarding soient chargées avant de prendre une décision
    // Si onboardingData est complètement vide, c'est qu'on est encore en train de charger
    const hasAnyOnboardingData = onboardingData.photo || onboardingData.personalInfo || onboardingData.appearance;
    const isDataLoaded = hasAnyOnboardingData || !loading; // Soit on a des données, soit le loading est fini

    if (!isDataLoaded) {
      return;
    }

    // Si l'utilisateur doit compléter son onboarding et n'est pas déjà sur la page
    if (shouldRedirectToOnboarding() && !isOnOnboardingPage) {
      // navigate('/onboarding', { replace: true });
    }
  }, [
    shouldRedirectToOnboarding, 
    loading, 
    isAuthenticated, 
    location.pathname, 
    navigate, 
    getCurrentStep,
    onboardingData
  ]);

  return <>{children}</>;
};

export default OnboardingGuard; 