import React from 'react';
import { useThemeClasses } from '../../theme/ThemeProvider';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  isLoading = false,
  className = '',
  id,
  ...props
}, ref) => {
  const themeClasses = useThemeClasses();
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const baseInputClasses = 'w-full px-4 py-3 text-sm border rounded-lg transition-all duration-200 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed text-neutral-900 bg-white';
  
  const inputClasses = error
    ? 'border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-opacity-20'
    : `border-neutral-300 ${themeClasses.focusPrimary} focus:border-[var(--color-primary-500)]`;

  const iconClasses = 'absolute top-1/2 transform -translate-y-1/2 text-neutral-400';

  return (
    <div className="space-y-2 w-full">
      {label && (
        <label 
          htmlFor={inputId}
          className={`block text-sm font-medium ${themeClasses.textNeutral}`}
        >
          {label}
          {props.required && <span className="text-black ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className={`${iconClasses} left-3`}>
            {leftIcon}
          </div>
        )}
        
        <input
          ref={ref}
          id={inputId}
          className={`
            ${baseInputClasses}
            ${inputClasses}
            ${leftIcon ? 'pl-10' : ''}
            ${rightIcon || isLoading ? 'pr-10' : ''}
            ${className}
          `}
          {...props}
        />
        
        {(rightIcon || isLoading) && (
          <div className={`${iconClasses} right-3`}>
            {isLoading ? (
              <svg
                className="animate-spin h-4 w-4 text-neutral-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
            ) : (
              rightIcon
            )}
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <p className={`text-xs ${error ? 'text-red-900' : 'text-white-500'}`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';