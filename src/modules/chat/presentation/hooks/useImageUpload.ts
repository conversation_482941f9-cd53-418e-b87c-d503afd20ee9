import { useState } from 'react';
import { UploadImageUseCase } from '../../application/usecases/UploadImageUseCase';
import { HttpChatRepository } from '../../infrastructure/repositories/HttpChatRepository';
import { ImageUploadResponseDto } from '../../infrastructure/api/types/ChatTypes';

const httpChatRepository = new HttpChatRepository();
const uploadImageUseCase = new UploadImageUseCase(httpChatRepository);

export const useImageUpload = () => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadImage = async (
    file: File, 
    receiverId: string, 
    caption?: string
  ): Promise<ImageUploadResponseDto> => {
    try {
      setIsUploading(true);
      setError(null);
      const result = await uploadImageUseCase.execute({
        file,
        receiverId,
        caption
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erreur lors de l\'upload';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  return {
    uploadImage,
    isUploading,
    error,
    clearError
  };
}; 