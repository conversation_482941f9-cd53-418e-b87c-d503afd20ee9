import React, { useState } from 'react';
import { ImageMessageContent } from '../../infrastructure/api/types/ChatTypes';
import { cn } from '@/modules/shared/infrastructure';
import { ImageViewer } from './ImageViewer';

interface ImageMessageProps {
  imageData: ImageMessageContent;
  isFromCurrentUser: boolean;
  className?: string;
  onImageLoad?: () => void;
}

export const ImageMessage: React.FC<ImageMessageProps> = ({
  imageData,
  isFromCurrentUser,
  className,
  onImageLoad
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const handleImageLoad = () => {
    setIsLoading(false);
    if (onImageLoad) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          onImageLoad();
        });
      });
    }
  };

  const handleImageError = () => {
    setIsLoading(false);
    setHasError(true);
    if (onImageLoad) {
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          onImageLoad();
        });
      });
    }
  };

  const openViewer = () => {
    if (!hasError) {
      setIsViewerOpen(true);
    }
  };

  const closeViewer = () => {
    setIsViewerOpen(false);
  };

  return (
    <>
      <div className={cn('max-w-xs', className)}>
        {/* Image container */}
        <div 
          className={cn(
            'relative rounded-lg overflow-hidden border cursor-pointer transition-opacity hover:opacity-90',
            isFromCurrentUser ? 'bg-primary/10' : 'bg-muted'
          )}
          onClick={openViewer}
        >
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-muted">
              <div className="animate-spin h-6 w-6 border-2 border-current border-t-transparent rounded-full" />
            </div>
          )}
          
          {hasError ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <div className="w-12 h-12 mb-2 rounded-full bg-destructive/10 flex items-center justify-center">
                <svg 
                  className="w-6 h-6 text-destructive" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
              </div>
              <p className="text-sm text-muted-foreground mb-1">
                Unable to load image
              </p>
              <p className="text-xs text-muted-foreground">
                Image non disponible
              </p>
            </div>
          ) : (
            <img
              src={imageData.url}
              alt={imageData.caption || imageData.originalName}
              onLoad={handleImageLoad}
              onError={handleImageError}
              className={cn(
                'w-full h-auto max-h-64 object-cover',
                isLoading && 'opacity-0'
              )}
            />
          )}
        </div>

        {/* Caption */}
        {imageData.caption && (
          <div className="mt-2">
            <p className="text-sm whitespace-pre-wrap text-black/70 break-words">
              {imageData.caption}
            </p>
          </div>
        )}
      </div>

      {/* Image Viewer */}
      <ImageViewer
        isOpen={isViewerOpen}
        onClose={closeViewer}
        imageData={imageData}
      />
    </>
  );
}; 