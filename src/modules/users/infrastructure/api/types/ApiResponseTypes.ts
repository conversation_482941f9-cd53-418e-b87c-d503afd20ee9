import { Packs } from "@/modules/packs/domain/entities/Packs";
import { UserBlockStatus } from "@/modules/users/domain/entities/User";

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface UserSearchResponse {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  isOnline?: boolean;
  profilePhoto?: string;
  firstName?: string;
  lastName?: string;
  birthdate?: string;
  gender?: string;
  country?: string;
  ethnicity?: string;
  height?: number;
  isOnboardingComplete?: boolean;
  profileCompletionPercentage?: number;
  interactionType?: "like" | "dislike" | "NONE";
  blockStatus?: UserBlockStatus; // Ajout du statut de blocage
  roles?: string[];
  hairColor?: string;
  eyeColor?: string;
  bodyType?: string;
  bodyColor?: string;
  religion?: string;
  isPaid?: boolean;
  isDeflouted?: boolean;
  totalCredit?: number;
  canLike?: boolean;
  canDefloutImage?: boolean;
  canSendMessage?: boolean;
  canViewListLikes?: boolean;
  canViewMessages?: boolean;
  totalCreditValueAndRemainingLikes?: number;
  totalCreditValueAndRemainingDefloutedImages?: number;
  totalCreditValueAndRemainingMessages?: number;
  packs?: Packs[];
}
