import { UserRepository } from "../../domain/repositories/UserRepository";
import { User, UserBlockStatus } from "../../domain/entities/User";
import { UserProfile } from "../../domain/valueobjects/UserProfile";
import { OnboardingData } from "../../application/usecases/CompleteOnboardingUseCase";
import { OnboardingPhoto } from "../../domain/valueobjects/OnboardingPhoto";
import { PersonalInfo } from "../../domain/valueobjects/PersonalInfo";
import { Appearance } from "../../domain/valueobjects/Appearance";
import { userApiClient } from "./UserApiClient";
import {
  SearchUsersParams,
  SearchUsersResult,
} from "../../domain/repositories/UserRepository";

export class UserApiRepository implements UserRepository {
  async searchUsers(searchTerm: string): Promise<User[]> {
    try {
      const response = await userApiClient.searchUsers(searchTerm);

      if (response.success && response.data) {
        return response.data.map((userData) =>
          User.create({
            id: userData.id,
            username: userData.username,
            email: userData.email,
            avatar: userData.avatar,
            isOnline: userData.isOnline,
            profilePhoto: userData.profilePhoto,
            firstName: userData.firstName,
            lastName: userData.lastName,
            birthdate: userData.birthdate,
            gender: userData.gender,
            country: userData.country,
            ethnicity: userData.ethnicity,
            height: userData.height,
            isOnboardingComplete: userData.isOnboardingComplete,
            profileCompletionPercentage: userData.profileCompletionPercentage,
            blockStatus: userData.blockStatus || UserBlockStatus.NONE,
            roles: userData.roles || [],
            hairColor: userData.hairColor,
            eyeColor: userData.eyeColor,
            bodyType: userData.bodyType,
            bodyColor: userData.bodyColor,
            religion: userData.religion,
            isPaid: userData.isPaid as boolean,
            isDeflouted: userData.isDeflouted,
          })
        );
      }

      return [];
    } catch (error) {
      console.error("Failed to search users:", error);
      throw new Error("Failed to search users");
    }
  }

  async getUserSuggestions(limit: number = 5): Promise<User[]> {
    try {
      const response = await userApiClient.getUserSuggestions(limit);

      if (response.success && response.data) {
        return response.data.map((userData) =>
          User.create({
            id: userData.id,
            username: userData.username,
            email: userData.email,
            avatar: userData.avatar,
            isOnline: userData.isOnline,
            profilePhoto: userData.profilePhoto,
            firstName: userData.firstName,
            lastName: userData.lastName,
            birthdate: userData.birthdate,
            gender: userData.gender,
            country: userData.country,
            ethnicity: userData.ethnicity,
            height: userData.height,
            isOnboardingComplete: userData.isOnboardingComplete,
            profileCompletionPercentage: userData.profileCompletionPercentage,
            interactionType: userData.interactionType,
            blockStatus: userData.blockStatus || UserBlockStatus.NONE,
            roles: userData.roles || [],
            hairColor: userData.hairColor,
            eyeColor: userData.eyeColor,
            bodyType: userData.bodyType,
            bodyColor: userData.bodyColor,
            religion: userData.religion,
            isPaid: userData.isPaid as boolean,
            isDeflouted: userData.isDeflouted,
          })
        );
      }

      return [];
    } catch (error) {
      console.error("Failed to get user suggestions:", error);
      throw new Error("Failed to get user suggestions");
    }
  }

  async getUserById(userId: string): Promise<User> {
    try {
      const response = await userApiClient.getUserById(userId);

      if (response.success && response.data) {
        return User.create({
          id: response.data.id,
          username: response.data.username,
          email: response.data.email,
          avatar: response.data.avatar,
          isOnline: response.data.isOnline,
          profilePhoto: response.data.profilePhoto,
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          birthdate: response.data.birthdate,
          gender: response.data.gender,
          country: response.data.country,
          ethnicity: response.data.ethnicity,
          height: response.data.height,
          isOnboardingComplete: response.data.isOnboardingComplete,
          profileCompletionPercentage:
            response.data.profileCompletionPercentage,
          interactionType: response.data.interactionType || "NONE",
          blockStatus: response.data.blockStatus || UserBlockStatus.NONE,
          roles: response.data.roles || [],
          hairColor: response.data.hairColor,
          eyeColor: response.data.eyeColor,
          bodyType: response.data.bodyType,
          bodyColor: response.data.bodyColor,
          religion: response.data.religion,
          isPaid: response.data.isPaid as boolean,
          isDeflouted: response.data.isDeflouted,
        });
      }

      throw new Error("User not found");
    } catch (error) {
      console.error(`Failed to get user ${userId}:`, error);
      throw new Error(
        `Failed to get user: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getConnectedUserProfile(): Promise<User | null> {
    try {
      const response = await userApiClient.getConnectedUserProfile();

      if (response.success && response.data) {
        return User.create({
          id: response.data.id,
          username: response.data.username,
          email: response.data.email,
          avatar: response.data.avatar,
          isOnline: response.data.isOnline,
          profilePhoto: response.data.profilePhoto,
          firstName: response.data.firstName,
          lastName: response.data.lastName,
          birthdate: response.data.birthdate,
          gender: response.data.gender,
          country: response.data.country,
          ethnicity: response.data.ethnicity,
          height: response.data.height,
          isOnboardingComplete: response.data.isOnboardingComplete,
          profileCompletionPercentage:
            response.data.profileCompletionPercentage,
          interactionType: response.data.interactionType || "NONE",
          blockStatus: response.data.blockStatus || UserBlockStatus.NONE,
          roles: response.data.roles || [],
          hairColor: response.data.hairColor,
          eyeColor: response.data.eyeColor,
          bodyType: response.data.bodyType,
          bodyColor: response.data.bodyColor,
          religion: response.data.religion,
          isPaid: response.data.isPaid,
          totalCredit: response.data.totalCredit,
          canLike: response.data.canLike,
          canDefloutImage: response.data.canDefloutImage,
          canSendMessage: response.data.canSendMessage,
          canViewListLikes: response.data.canViewListLikes,
          canViewMessages: response.data.canViewMessages,
          totalCreditValueAndRemainingLikes:
            response.data.totalCreditValueAndRemainingLikes,
          totalCreditValueAndRemainingDefloutedImages:
            response.data.totalCreditValueAndRemainingDefloutedImages,
          totalCreditValueAndRemainingMessages:
            response.data.totalCreditValueAndRemainingMessages,
          packs: response.data.packs,
        });
      }

      return null;
    } catch (error) {
      console.error(`Failed to get connected user profile:`, error);
      return null;
    }
  }

  async updateUserProfile(userId: string, userProfile: any): Promise<void> {
    try {
      // Convertir les value objects en format API
      const profileData: any = {};

      if (userProfile.personalInfo) {
        profileData.personalInfo = userProfile.personalInfo.toApiFormat
          ? userProfile.personalInfo.toApiFormat()
          : userProfile.personalInfo;
      }

      if (userProfile.photos) {
        // userProfile.photos est maintenant un tableau de ProfilePhoto (après conversion depuis OnboardingPhoto)
        profileData.photos = Array.isArray(userProfile.photos)
          ? userProfile.photos
          : [userProfile.photos];
      }

      if (userProfile.appearance) {
        profileData.appearance = userProfile.appearance.toApiFormat
          ? userProfile.appearance.toApiFormat()
          : userProfile.appearance;
      }

      profileData.isProfileComplete = userProfile.isProfileComplete || false;

      const response = await userApiClient.updateUserProfile(
        userId,
        profileData
      );

      if (!response.success) {
        throw new Error(response.message || "Failed to update user profile");
      }
    } catch (error) {
      console.error(`Failed to update user profile for ${userId}:`, error);
      throw new Error(
        `Failed to update user profile: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async updateOnboardingStep(
    userId: string,
    step: number,
    data: any
  ): Promise<void> {
    try {
      // Convertir les value objects en format API si nécessaire
      let stepData = data;

      if (data instanceof OnboardingPhoto) {
        stepData = data.toApiFormat();
      } else if (data && typeof data.toApiFormat === "function") {
        stepData = data.toApiFormat();
      }

      const response = await userApiClient.updateOnboardingStep(
        userId,
        step,
        stepData
      );

      if (!response.success) {
        throw new Error(response.message || "Failed to update onboarding step");
      }
    } catch (error) {
      console.error(
        `Failed to update onboarding step ${step} for user ${userId}:`,
        error
      );
      throw new Error(
        `Failed to update onboarding step: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const response = await userApiClient.getUserProfile(userId);

      if (response.success && response.data) {
        // Note: UserProfile est maintenant obsolète avec la nouvelle structure
        // Cette méthode retournera null pour l'instant jusqu'à refactorisation complète
        console.warn(
          "getUserProfile: UserProfile class is deprecated, returning null"
        );
        return null;
      }

      return null;
    } catch (error) {
      console.error(`Failed to get user profile for ${userId}:`, error);
      return null;
    }
  }

  async getUserOnboardingData(): Promise<OnboardingData | null> {
    try {
      const response = await userApiClient.getUserOnboardingData();

      if (response.success && response.data) {
        const data: OnboardingData = {};

        // Convertir les données API en value objects
        if (response.data.photo) {
          try {
            data.photo = OnboardingPhoto.create(
              response.data.photo.id,
              response.data.photo.url,
              response.data.photo.fileName
            );
          } catch (error) {
            console.warn("Invalid photo data, skipping:", error);
          }
        }

        if (response.data.personalInfo) {
          try {
            // Utiliser la méthode statique create de PersonalInfo
            data.personalInfo = PersonalInfo.create(
              response.data.personalInfo.firstName,
              response.data.personalInfo.lastName,
              new Date(response.data.personalInfo.birthdate),
              response.data.personalInfo.gender,
              response.data.personalInfo.country,
              response.data.personalInfo.relationType
            );
          } catch (error) {
            console.warn("Invalid personal info data, skipping:", error);
          }
        }

        if (response.data.appearance) {
          try {
            data.appearance = new Appearance(
              response.data.appearance.ethnicity,
              response.data.appearance.height,
              response.data.appearance.bodyType,
              response.data.appearance.hairColor,
              response.data.appearance.eyeColor,
              response.data.bodyColor,
              response.data.appearance.religion
            );
          } catch (error) {
            console.warn("Invalid appearance data, skipping:", error);
          }
        }

        return data;
      }

      return null;
    } catch (error) {
      console.error(`Failed to get user onboarding data:`, error);
      return null;
    }
  }

  async completeOnboarding(
    onboardingData: OnboardingData
  ): Promise<{ success: boolean; profileCompletionPercentage: number }> {
    try {
      // Convertir les value objects en format API
      const apiData: any = {};

      if (onboardingData.photo) {
        apiData.photo = {
          profilePhoto: onboardingData.photo.url,
        };
      }

      if (onboardingData.personalInfo) {
        apiData.personalInfo = {
          firstName: onboardingData.personalInfo.firstName.toString(),
          lastName: onboardingData.personalInfo.lastName.toString(),
          birthdate: onboardingData.personalInfo.birthdate.value
            .toISOString()
            .split("T")[0],
          gender: onboardingData.personalInfo.gender,
          country: onboardingData.personalInfo.country.getCode(),
          relationType: onboardingData.personalInfo.relationType,
        };
      }

      if (onboardingData.appearance) {
        apiData.appearance = {
          ethnicity: onboardingData.appearance.ethnicity,
          height: onboardingData.appearance.height,
          bodyType: onboardingData.appearance.bodyType,
          hairColor: onboardingData.appearance.hairColor,
          eyeColor: onboardingData.appearance.eyeColor,
          bodyColor: onboardingData.appearance.bodyColor,
          religion: onboardingData.appearance.religion,
        };
      }

      const response = await userApiClient.completeOnboarding(apiData);

      if (response.success && response.data) {
        return {
          success: true,
          profileCompletionPercentage:
            response.data.profileCompletionPercentage,
        };
      }

      throw new Error(response.message || "Failed to complete onboarding");
    } catch (error) {
      console.error("Failed to complete onboarding:", error);
      throw new Error(
        `Failed to complete onboarding: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async searchUsersWithFilters(
    params: SearchUsersParams
  ): Promise<SearchUsersResult> {
    console.log("searchUsersWithFilters params:", params);
    try {
      const response = await userApiClient.searchUsersWithFilters({
        searchTerm: params.searchTerm,
        limit: params.limit || 5,
        offset: params.offset || 0,
        ...(params.profileFilters || {}),
      });

      if (response.success && response.data && Array.isArray(response.data)) {
        const users = response.data.map((userData) =>
          User.create({
            id: userData.id,
            username: userData.username,
            email: userData.email,
            avatar: userData.avatar,
            isOnline: userData.isOnline,
            profilePhoto: userData.profilePhoto,
            firstName: userData.firstName,
            lastName: userData.lastName,
            birthdate: userData.birthdate,
            gender: userData.gender,
            country: userData.country,
            ethnicity: userData.ethnicity,
            height: userData.height,
            isOnboardingComplete: userData.isOnboardingComplete,
            profileCompletionPercentage: userData.profileCompletionPercentage,
            interactionType: userData.interactionType,
            blockStatus: userData.blockStatus || UserBlockStatus.NONE,
            roles: userData.roles || [],
            hairColor: userData.hairColor,
            eyeColor: userData.eyeColor,
            bodyType: userData.bodyType,
            bodyColor: userData.bodyColor,
            religion: userData.religion,
            isPaid: userData.isPaid as boolean,
            isDeflouted: userData.isDeflouted,
          })
        );

        return {
          users,
          hasMore: response.hasMore || false,
          pagination: response.pagination || {
            offset: params.offset || 0,
            limit: params.limit || 5,
          },
        };
      }

      return {
        users: [],
        hasMore: false,
        pagination: { offset: 0, limit: 5 },
      };
    } catch (error) {
      console.error("Failed to search users with filters:", error);
      throw new Error(
        `Failed to search users with filters: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async updateConnectedUserProfile(
    id: string,
    userProfile: any
  ): Promise<void> {
    try {
      const response = await userApiClient.updateConnectedUserProfile(
        id,
        userProfile
      );

      if (!response.success) {
        throw new Error(
          response.message || "Failed to update connected user profile"
        );
      }
    } catch (error) {
      console.error(
        `Failed to update connected user profile for ${id}:`,
        error
      );
      throw new Error(
        `Failed to update connected user profile: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async reportUser(data: {
    reportedByID: string;
    reportedToID: string;
    reason: string;
  }): Promise<void> {
    console.log("reportUser data:", data);
    try {
      const response = await userApiClient.reportUser(data);

      if (!response.success) {
        throw new Error(response.message || "Failed to report user");
      }
    } catch (error) {
      console.error("Failed to report user:", error);
      throw new Error(
        `Failed to report user: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async getReportUserByUserId(): Promise<User[] | null> {
    try {
      const response = await userApiClient.getReportUserByUserId();

      if (response.success && response.data) {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error(`Failed to get report user:`, error);
      throw new Error(
        `Failed to get report user: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async updateBlockStatus(
    userId: string,
    blockStatus: UserBlockStatus
  ): Promise<void> {
    try {
      const response = await userApiClient.updateBlockStatus(
        userId,
        blockStatus
      );

      if (!response.success) {
        throw new Error(response.message || "Failed to update block status");
      }
    } catch (error) {
      console.error(`Failed to update block status for user ${userId}:`, error);
      throw new Error(
        `Failed to update block status: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async defloutedUserPhoto(data: any): Promise<void> {
  try {
    const response = await userApiClient.defloutedUserPhoto(data);
    console.log("Response from defloutedUserPhoto API:", response);

    if (!response.success) {
      throw new Error(response.message || "Failed to defloute user photo");
    }
  } catch (error) {
    console.error(`Failed to defloute user photo:`, error);
    throw new Error(
      `Failed to defloute user photo: ${
        error instanceof Error ? error.message : "Unknown error"
      }`
    );
  }
}

}

// Export singleton instance
export const userApiRepository = new UserApiRepository();
export default userApiRepository;
