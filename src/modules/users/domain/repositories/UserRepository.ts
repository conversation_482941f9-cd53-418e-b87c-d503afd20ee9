import { User, UserBlockStatus } from "../entities/User";
import { UserProfile } from "../valueobjects/UserProfile";
import { OnboardingData } from "../../application/usecases/CompleteOnboardingUseCase";

export interface UserProfileFilters {
  gender?: string;
  minAge?: number;
  maxAge?: number;
  country?: string;
  ethnicity?: string;
  minHeight?: number;
  maxHeight?: number;
  isOnline?: boolean;
  relationType?: string;
  hairColor?: string;
  eyeColor?: string;
  bodyType?: string;
  bodyColor?: string;
  religion?: string;
  isPaid?: boolean;
}

export interface SearchUsersParams {
  searchTerm: string;
  limit?: number;
  offset?: number;
  profileFilters?: UserProfileFilters;
}

export interface SearchUsersResult {
  users: User[];
  hasMore: boolean;
  pagination: {
    offset: number;
    limit: number;
  };
}

export interface UserRepository {
  searchUsers(searchTerm: string): Promise<User[]>;
  searchUsersWithFilters(params: SearchUsersParams): Promise<SearchUsersResult>;
  getUserSuggestions(limit?: number): Promise<User[]>;
  getUserById(userId: string): Promise<User>;
  updateUserProfile(userId: string, userProfile: UserProfile): Promise<void>;
  updateOnboardingStep(userId: string, step: number, data: any): Promise<void>;
  getUserProfile(userId: string): Promise<UserProfile | null>;
  getUserOnboardingData(): Promise<OnboardingData | null>;
  completeOnboarding(
    onboardingData: OnboardingData
  ): Promise<{ success: boolean; profileCompletionPercentage: number }>;
  getConnectedUserProfile(): Promise<User | null>;
  updateConnectedUserProfile(id: string, userProfile: any): Promise<void>;
  reportUser(data: {
    reportedByID: string;
    reportedToID: string;
    reason: string;
  }): Promise<void>;
  getReportUserByUserId(userId: string): Promise<User[] | null>;
  updateBlockStatus(
    userId: string,
    blockStatus: UserBlockStatus
  ): Promise<void>;

  defloutedUserPhoto(data: any, token: string): Promise<void>;
}
