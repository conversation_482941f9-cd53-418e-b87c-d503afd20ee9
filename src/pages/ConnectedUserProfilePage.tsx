import { ChatLayout } from "@/components/ChatLayout";
import { Packs } from "@/modules/packs/domain/entities/Packs";
import { <PERSON><PERSON>, Card } from "@/modules/shared";
import { User } from "@/modules/users/domain/entities/User";
import { Country } from "@/modules/users/domain/valueobjects/Country";
import userApiRepository from "@/modules/users/infrastructure/api/UserApiRepository";
import {
  Baby,
  Calendar,
  Church,
  Edit2Icon,
  Eye,
  Heart,
  MapPin,
  Ruler,
  UserIcon,
  UserPlus,
  Users,
  Filter,
} from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";

interface PackDisplayProps {
  packs: Packs[];
  user: User;
}

type PackName = "free" | "select_24h" | "discover_72h" | "conclude_96h";
type FilterType = "all" | PackName;

const PacksSection: React.FC<PackDisplayProps> = ({ packs, user }) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>("all");

  const getPackDisplayName = (packName: string): string => {
    switch (packName) {
      case "free":
        return "FREE ACCESS";
      case "select_24h":
        return "24H TO SELECT";
      case "discover_72h":
        return "72H TO DISCOVER";
      case "conclude_96h":
        return "96H TO CONCLUDE";
      default:
        return packName.toUpperCase();
    }
  };

  // Group packs by pack_name
  const groupedPacks: Record<string, Packs[]> = packs.reduce(
    (groups: Record<string, Packs[]>, pack: Packs) => {
      const key = pack.pack_name;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(pack);
      return groups;
    },
    {}
  );

  // Filter packs based on active filter
  const filteredPacks: Packs[] =
    activeFilter === "all"
      ? packs
      : packs.filter((pack: Packs) => pack.pack_name === activeFilter);

  // Get unique pack names for filter buttons
  const uniquePackNames: string[] = Array.from(
    new Set(packs.map((pack: Packs) => pack.pack_name))
  );

  if (packs.length === 0) {
    return null;
  }

  return (
    <div className="mb-6">
      {/* Filter Buttons */}
      <div className="flex flex-wrap justify-center gap-2 mb-6">
        <button
          onClick={() => setActiveFilter("all")}
          className={`px-3 py-1.5 text-sm rounded-full font-medium transition-all duration-300 flex items-center space-x-2 ${
            activeFilter === "all"
              ? "bg-indigo-500 text-white shadow-lg"
              : "bg-black/10 text-black/60 hover:bg-black/20"
          }`}
        >
          <Filter className="h-4 w-4" />
          <span>
            All ({packs.length})
          </span>
        </button>
        {uniquePackNames.map((packName: string) => (
          <button
            key={packName}
            onClick={() => setActiveFilter(packName as FilterType)}
            className={`px-3 py-1.5 text-sm rounded-full font-medium transition-all duration-300 ${
              activeFilter === packName
                ? "bg-indigo-500 text-white shadow-lg"
                : "bg-black/10 text-black/60 hover:bg-black/20"
            }`}
          >
            {getPackDisplayName(packName)}
          </button>
        ))}
      </div>

      {/* Display Mode: Grouped or Filtered */}
      {activeFilter === "all" ? (
        // Grouped view
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {Object.entries(groupedPacks).map(
            ([packName, packList]: [string, Packs[]]) => (
              <div key={packName} className="w-full">
                {packList.map((pack) => (
                  <PackCard key={pack.id} pack={pack} />
                ))}
              </div>
            )
          )}
        </div>
      ) : (
        // Filtered view

        <div className="grid grid-cols-1 gap-4">
          {filteredPacks.map((pack) => (
            <PackCard key={pack.id} pack={pack} />
          ))}
        </div>
      )}
    </div>
  );
};

interface PackCardProps {
  pack: Packs;
}

const PackCard: React.FC<PackCardProps> = ({ pack }) => {
  const getPackDisplayName = (packName: string): string => {
    switch (packName) {
      case "free":
        return "FREE ACCESS";
      case "select_24h":
        return "24h TO SELECT";
      case "discover_72h":
        return "72h TO DISCOVER";
      case "conclude_96h":
        return "96h TO CONCLUDE";
      default:
        return packName.toUpperCase();
    }
  };

  const getPackColor = (packName: string): string => {
    switch (packName) {
      case "free":
        return "from-green-400 to-emerald-500";
      case "select_24h":
        return "from-blue-400 to-cyan-500";
      case "discover_72h":
        return "from-purple-400 to-indigo-500";
      case "conclude_96h":
        return "from-orange-400 to-red-500";
      default:
        return "from-gray-400 to-gray-500";
    }
  };

  const formatCount = (
    count: number,
    singular: string,
    plural: string
  ): string => {
    return count > 0 ? `${count} ${plural}` : `0 ${singular}`;
  };

  const formatDate = (date: Date | string | null | undefined): string => {
    if (!date) return "Until credits are exhausted";

    const parsedDate = typeof date === "string" ? new Date(date) : date;

    if (!(parsedDate instanceof Date) || isNaN(parsedDate.getTime())) {
      return "Invalid date";
    }

    const datePart = parsedDate.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long", // 👉 "July"
      day: "numeric",
    });

    const timePart = parsedDate.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true, // 👉 AM/PM format
    });

    return `${datePart} at ${timePart}`;
  };

  return (
    <Card className="overflow-hidden border-0 shadow-lg backdrop-blur-sm bg-white/80 hover:shadow-xl transition-all duration-300">
      <div
        className={`bg-gradient-to-r ${getPackColor(
          pack.pack_name
        )} p-3 text-white`}
      >
        <h5 className="font-bold text-center text-sm uppercase">
          {getPackDisplayName(pack.pack_name)}
        </h5>
      </div>
      <div className="p-4 space-y-2">
        <div className="text-center">
          <p className="text-black/60 text-xs">Credits</p>
          <p className="font-bold text-indigo-600">{pack.credit_value}</p>
        </div>
        <div className="grid grid-cols-3 gap-2 text-sm">
          {/* <div className="text-center">
            <p className="text-black/60 text-xs">Price</p>
            <p className="font-bold text-green-600">{pack.price} €</p>
          </div> */}
          <div className="text-center">
            <p className="text-black/60 text-xs">Remaining likes</p>
            <p className="font-bold text-pink-600 text-xs">
              {pack.pack_name === "free"
                ? "Unlimited"
                : formatCount(pack.remaining_likes, "like", "likes")}
            </p>
          </div>
          <div className="text-center">
            <p className="text-black/60 text-xs">Remaining Messages</p>
            <p className="font-bold text-blue-600 text-xs">
              {pack.pack_name === "free"
                ? "Unlimited"
                : formatCount(pack.remaining_messages, "message", "messages")}
            </p>
          </div>
          <div className="text-center">
            <p className="text-black/60 text-xs">Remaining images</p>
            <p className="font-bold text-purple-600 text-xs">
              {pack.pack_name === "free"
                ? "Unlimited"
                : formatCount(
                    pack.remaining_deflouted_images,
                    "image",
                    "images"
                  )}
            </p>
          </div>
        </div>

        <div className="text-center pt-2 border-t border-black/10">
          <p className="text-black/60 text-xs">Expiry Date</p>
          <p className="font-bold text-black text-xs">
            {formatDate(pack.expiry_date)}
          </p>
        </div>
      </div>
    </Card>
  );
};

export const ConnectedUserProfilePage = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"personal" | "appearance">(
    "personal"
  );

  const AllCountries = Country.getAllSupported();

  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const userData = await userApiRepository.getConnectedUserProfile();
        setUser(userData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err.message
            : "Erreur lors du chargement du profil"
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadUserProfile();
  }, []);

  const getInitials = (username: string): string => {
    return username
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getAvatarGradient = (userId: string) => {
    const gradients = [
      "from-blue-400 via-purple-500 to-pink-500",
      "from-green-400 via-blue-500 to-purple-600",
      "from-pink-400 via-red-500 to-yellow-500",
      "from-indigo-400 via-purple-500 to-pink-500",
      "from-cyan-400 via-blue-500 to-indigo-600",
      "from-orange-400 via-pink-500 to-red-500",
    ];
    const index = userId.length % gradients.length;
    return gradients[index];
  };

  if (isLoading) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <Card className="p-8 border-0 shadow-2xl backdrop-blur-xl">
              <div className="text-center space-y-6">
                <div className="w-32 h-32 bg-white/20 rounded-full mx-auto"></div>
                <div className="h-8 bg-white/20 rounded-full w-48 mx-auto"></div>
                <div className="h-4 bg-white/20 rounded-full w-32 mx-auto"></div>
                <div className="flex justify-center space-x-4">
                  <div className="w-12 h-12 bg-white/20 rounded-full"></div>
                  <div className="w-12 h-12 bg-white/20 rounded-full"></div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </ChatLayout>
    );
  }

  if (error) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <Card
            className="p-8 border-0 shadow-2xl backdrop-blur-xl"
            style={{
              background: `linear-gradient(135deg, 
                    rgba(239, 68, 68, 0.1) 0%, 
                    rgba(220, 38, 127, 0.05) 50%, 
                    rgba(239, 68, 68, 0.1) 100%
                  )`,
              backdropFilter: "blur(20px)",
              border: "1px solid rgba(239, 68, 68, 0.2)",
            }}
          >
            <div className="text-center">
              <div className="text-red-400 mb-6 text-lg font-medium">
                {error}
              </div>
            </div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  if (!user) {
    return (
      <ChatLayout className="p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="p-12 border-0 shadow-2xl backdrop-blur-xl">
            <div className="text-center text-white/80">
              <p className="text-xl font-semibold mb-3 text-gray-400">
                User not found
              </p>
              <p className="text-sm mb-6 text-gray-400">
                This user does not exist or has been deleted
              </p>
            </div>
          </Card>
        </div>
      </ChatLayout>
    );
  }

  return (
    <ChatLayout className="p-6 mt-12 lg:mt-0 overflow-y-auto">
      <div className="max-w-4xl mx-auto">
        {/* Profil principal */}
        <Card className="p-8 border-0 shadow-2xl backdrop-blur-xl mb-6">
          <Button
            className="absolute top-4 right-4 flex items-center lg:space-x-2"
            onClick={() => navigate(`/edit-profile/${user.id}`)}
          >
            <Edit2Icon className="w-4 h-4" />
            <span className="hidden lg:inline">Edit</span>
          </Button>
          <div className="text-center">
            {/* Photo de profil */}
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-30 scale-110"></div>

                {user.profilePhoto ? (
                  <img
                    src={user.profilePhoto}
                    alt={`${user.username} profile picture`}
                    className={`w-32 h-32 rounded-full object-cover ring-4 ring-white/20 shadow-2xl relative z-10`}
                  />
                ) : (
                  <div
                    className={`w-32 h-32 rounded-full bg-gradient-to-br ${getAvatarGradient(
                      user.id
                    )} flex items-center justify-center shadow-2xl relative z-10 ring-4 ring-white/20`}
                  >
                    <span className="text-3xl font-bold text-white drop-shadow-lg">
                      {getInitials(user.username)}
                    </span>
                  </div>
                )}

                {/* Indicateur en ligne */}
                {user.isOnline && (
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full ring-4 ring-black/20 shadow-lg z-20">
                    <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-40"></div>
                    <div className="absolute inset-1 bg-green-400 rounded-full"></div>
                  </div>
                )}
              </div>
            </div>

            {/* Informations utilisateur */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-black mb-3 drop-shadow-sm">
                {user.username}
              </h1>
              {user.getFullName() && (
                <p className="text-black/70 mb-3">@{user.username}</p>
              )}
              <div className="flex justify-center items-center mb-6">
                {user.isOnline ? (
                  <span className="text-green-300 font-semibold flex items-center space-x-2 bg-green-500/20 backdrop-blur-sm px-4 py-2 rounded-full border border-green-400/30">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span>Online</span>
                  </span>
                ) : (
                  <span className="text-black/60 bg-black/10 backdrop-blur-sm px-4 py-2 rounded-full border border-white/20">
                    Offline
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Packs Section - Now properly organized */}
          {user.packs && user.packs.length > 0 && (
            <PacksSection packs={user.packs} user={user} />
          )}
        </Card>

        {/* Onglets */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-white/10 backdrop-blur-xl rounded-lg p-1 border border-white/20">
            <button
              onClick={() => setActiveTab("personal")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "personal"
                  ? "bg-gradient-to-r from-pink-500 to-purple-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <UserIcon className="h-4 w-4" />
              <span>Personal Information</span>
            </button>
            <button
              onClick={() => setActiveTab("appearance")}
              className={`flex-1 py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center space-x-2 ${
                activeTab === "appearance"
                  ? "bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-lg"
                  : "text-black/70 bg-black/10 hover:text-white hover:bg-black/10"
              }`}
            >
              <Users className="h-4 w-4" />
              <span>Appearance</span>
            </button>
          </div>
        </div>

        {/* Contenu des onglets */}
        {activeTab === "personal" && (
          <Card className="p-6 border-0 shadow-xl backdrop-blur-xl">
            <h3 className="text-lg font-semibold text-black mb-6 flex items-center space-x-2">
              <UserIcon className="h-5 w-5 text-pink-400" />
              <span>Personal Information</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.firstName && (
                <div className="flex hidden items-center space-x-3">
                  <UserPlus className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">First Name</p>
                    <p className="text-black font-medium">{user.firstName}</p>
                  </div>
                </div>
              )}
              {user.lastName && (
                <div className="flex hidden items-center space-x-3">
                  <UserPlus className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Last Name</p>
                    <p className="text-black font-medium">{user.lastName}</p>
                  </div>
                </div>
              )}
              {user.getAge() && (
                <div className="flex items-center space-x-3">
                  <Baby className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Age</p>
                    <p className="text-black font-medium">
                      {user.getAge()} years old
                    </p>
                  </div>
                </div>
              )}
              {user.getGenderDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Gender</p>
                    <p className="text-black font-medium">
                      {user.getGenderDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.country && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Country</p>
                    <p className="text-black font-medium">
                      {AllCountries.find((c) => c.code === user.country)
                        ?.name || user.country}
                    </p>
                  </div>
                </div>
              )}
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-black/60" />
                <div>
                  <p className="text-sm text-black/60">Member since</p>
                  <p className="text-black font-medium">Recently</p>
                </div>
              </div>
            </div>

            {!user.firstName &&
              !user.lastName &&
              !user.getAge() &&
              !user.getGenderDisplay() &&
              !user.country && (
                <div className="text-center py-8">
                  <UserIcon className="h-12 w-12 mx-auto text-black/30 mb-4" />
                  <p className="text-black/60">
                    No personal information available
                  </p>
                </div>
              )}
          </Card>
        )}

        {activeTab === "appearance" && (
          <Card className="p-6 border-0 shadow-xl backdrop-blur-xl">
            <h3 className="text-lg font-semibold text-black mb-6 flex items-center space-x-2">
              <Users className="h-5 w-5 text-purple-400" />
              <span>Appearance</span>
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {user.getEthnicityDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Ethnicity</p>
                    <p className="text-black font-medium">
                      {user.getEthnicityDisplay()}
                    </p>
                  </div>
                </div>
              )}

              {user.getHeightDisplay() && (
                <div className="flex items-center space-x-3">
                  <Ruler className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Height</p>
                    <p className="text-black font-medium">
                      {user.getHeightDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getHairColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Hair Color</p>
                    <p className="text-black font-medium">
                      {user.getHairColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getEyeColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Eye className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Eye Color</p>
                    <p className="text-black font-medium">
                      {user.getEyeColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getBodyColorDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Body Color</p>
                    <p className="text-black font-medium">
                      {user.getBodyColorDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getBodyTypeDisplay() && (
                <div className="flex items-center space-x-3">
                  <Users className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Body Type</p>
                    <p className="text-black font-medium">
                      {user.getBodyTypeDisplay()}
                    </p>
                  </div>
                </div>
              )}
              {user.getReligionDisplay() && (
                <div className="flex items-center space-x-3">
                  <Church className="h-4 w-4 text-black/60" />
                  <div>
                    <p className="text-sm text-black/60">Religion</p>
                    <p className="text-black font-medium">
                      {user.getReligionDisplay()}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {!user.getEthnicityDisplay() && !user.getHeightDisplay() && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 mx-auto text-black/30 mb-4" />
                <p className="text-black/60">
                  No appearance information available
                </p>
              </div>
            )}
          </Card>
        )}
      </div>
    </ChatLayout>
  );
};
