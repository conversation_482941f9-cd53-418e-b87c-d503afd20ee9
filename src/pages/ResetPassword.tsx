import { Button, Input, Label } from "@/modules/shared/presentation";
import { useParams } from "react-router-dom";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { useState } from "react";
import { Password } from "@/modules/auth/domain/valueobjects/Password";
import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { PasswordTooShortException } from "@/modules/auth/domain/exceptions/PasswordTooShort";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/modules/shared/presentation";

export const ResetPasswordPage = () => {
  const { token } = useParams();
  const { resetPassword } = useAuth();
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (token) {
      if (newPassword !== confirmPassword) {
        setError("Passwords do not match");
        return;
      }
      try {
        const password = new Password(newPassword);
        resetPassword(token, password.getValue());
      } catch (error) {
        if (error instanceof InvalidPasswordException) {
          setError(
            "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
          );
        } else if (error instanceof PasswordTooShortException) {
          setError("Password must be at least 8 characters long");
        } else {
          setError("An error occurred");
        }
      }
    }
  };

  return (
    <div className="flex items-center justify-center p-4 min-h-screen">
      <div className="w-full max-w-md">
        <div className="text-center mb-12">
          <div className="relative inline-block mb-6">
            {/* Halo effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-indigo-400/30 to-purple-400/30 rounded-full blur-xl scale-150 animate-pulse"></div>

            {/* Logo principal */}
            <div className="relative bg-gradient-to-br from-purple-500 via-pink-600 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl backdrop-blur-xl border border-white/20">
              <span className="text-3xl font-bold text-white drop-shadow-lg">
                T
              </span>
              {/* Icône clé décorative */}
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-amber-400 rounded-full animate-pulse">
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs">🔑</span>
                </div>
              </div>
              {/* Reflet */}
              <div className="absolute inset-2 bg-gradient-to-br from-white/30 to-transparent rounded-xl pointer-events-none"></div>
            </div>
          </div>

          <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-500 via-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
            Reset Password
          </h1>
          <p className="text-black/70 text-lg">Enter your new password</p>
        </div>

        <div className="relative group">
          <div className="bg-gradient-to-br from-pink-500 to-purple-600 p-8 shadow-2xl w-full max-w-md">
            <CardContent>
              {token ? (
                <>
                  {error && (
                    <div className="p-2 rounded-md bg-red-700">
                      <p className="text-white">{error}</p>
                    </div>
                  )}
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        type="password"
                        id="newPassword"
                        value={newPassword}
                        placeholder="New Password"
                        onChange={(e) => setNewPassword(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm Password</Label>
                      <Input
                        type="password"
                        id="confirmPassword"
                        value={confirmPassword}
                        placeholder="Confirm Password"
                        onChange={(e) => setConfirmPassword(e.target.value)}
                      />
                    </div>
                    <Button type="submit" className="w-full">
                      Reset Password
                    </Button>
                  </form>
                </>
              ) : (
                <div>Invalid url</div>
              )}
            </CardContent>
          </div>
        </div>
      </div>
    </div>
  );
};
