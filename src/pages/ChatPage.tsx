import React, { useEffect, useState, useRef, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Chat<PERSON>rovider,
  useChat,
  AuthDependency,
  UserSearchDependency,
  UserSearchResult,
} from "../modules/chat/presentation/context/ChatContext";
import { ChatMessage } from "../modules/chat/presentation/components/ChatMessage";
import { MessageInput } from "../modules/chat/presentation/components/MessageInput";
import { UserSearchModal } from "../modules/chat/presentation/components/UserSearchModal";
import { TypingIndicator } from "../modules/chat/presentation/components/TypingIndicator";
import { Button, useThemeClasses } from "@/modules/shared/presentation";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { useUserSearch } from "@/modules/users/presentation/hooks/useUserSearch";
import { PendingMedia } from "@/modules/chat/presentation/types/MessageTypes";
import { UserBlockStatus } from "@/modules/users/domain/entities/User";
import { MessageTypeEnum } from "@/modules/chat/domain/valueobjects/MessageType";
import { toast } from "react-toastify";

// Force this file to be treated as a module
export {};

// Component intérieur qui utilise le contexte
const ChatPageContent: React.FC = () => {
  const { state, actions } = useChat();
  const location = useLocation();
  const navigate = useNavigate();
  const [isUserSearchModalOpen, setIsUserSearchModalOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const unreadMessageRef = useRef<HTMLDivElement>(null);
  const [chatFirstOpenStates, setChatFirstOpenStates] = useState<
    Record<string, boolean>
  >({});
  const [isUnreadIndicatorVisible, setIsUnreadIndicatorVisible] =
    useState(true);
  const themeClasses = useThemeClasses();
  const { userInfos, loadUserInfos } = useAuth();

  // Utiliser un ref pour éviter les re-exécutions
  const hasInitializedFromNavigation = useRef(false);

  useEffect(() => {
    loadUserInfos();
  }, [loadUserInfos]);

  // Gérer l'initialisation d'une conversation depuis la navigation
  useEffect(() => {
    const locationState = location.state as {
      startChatWith?: UserSearchResult;
    };

    if (
      locationState?.startChatWith &&
      state.auth.currentUser?.id &&
      state.auth.isAuthenticated &&
      !hasInitializedFromNavigation.current
    ) {
      console.log(
        "Preparing chat interface for user:",
        locationState.startChatWith
      );
      hasInitializedFromNavigation.current = true;

      // Préparer l'interface de chat sans créer le chat en base
      const prepareChat = async () => {
        try {
          // Synchroniser l'utilisateur sélectionné avec le ChatContext
          actions.syncUser(
            locationState.startChatWith!.id,
            locationState.startChatWith!.username,
            locationState.startChatWith!.avatar,
            locationState.startChatWith!.isOnline,
            locationState.startChatWith!.blockStatus
          );

          // Créer un "chat virtuel" temporaire pour l'interface
          // Ce chat n'existe pas en base, il sera créé au premier message
          const virtualChat = {
            id: `virtual-${state.auth.currentUser!.id}-${
              locationState.startChatWith!.id
            }`,
            participants: [
              {
                id: state.auth.currentUser!.id,
                name: state.auth.currentUser!.username,
                email: state.auth.currentUser!.email,
                avatar: undefined,
                isOnline: true,
                blockStatus: state.auth.currentUser?.blockStatus,
                getId: () => state.auth.currentUser!.id,
                getName: () => state.auth.currentUser!.username,
                getEmail: () => state.auth.currentUser!.email,
                getAvatar: () => undefined,
                isUserOnline: () => true,
              },
              {
                id: locationState.startChatWith!.id,
                name: locationState.startChatWith!.username,
                email: locationState.startChatWith!.email,
                avatar: locationState.startChatWith!.avatar,
                isOnline: locationState.startChatWith!.isOnline || false,
                blockStatus: locationState.startChatWith?.blockStatus,
                getId: () => locationState.startChatWith!.id,
                getName: () => locationState.startChatWith!.username,
                getEmail: () => locationState.startChatWith!.email,
                getAvatar: () => locationState.startChatWith!.avatar,
                isUserOnline: () =>
                  locationState.startChatWith!.isOnline || false,
              },
            ],
            createdAt: new Date(),
            updatedAt: new Date(),
            lastMessage: undefined,
            isVirtual: true, // Flag pour identifier les chats virtuels
            targetUserId: locationState.startChatWith!.id, // ID de l'utilisateur cible
            getId: function () {
              return this.id;
            },
            getParticipants: function () {
              return this.participants;
            },
            getOtherParticipant: function (currentUserId: string) {
              return this.participants.find((p) => p.getId() !== currentUserId);
            },
            getDisplayName: function (currentUserId: string) {
              const otherParticipant = this.getOtherParticipant(currentUserId);
              return otherParticipant
                ? otherParticipant.getName()
                : "Utilisateur inconnu";
            },
            getCreatedAt: function () {
              return this.createdAt;
            },
            getUpdatedAt: function () {
              return this.updatedAt;
            },
            getLastMessage: function () {
              return this.lastMessage;
            },
          };

          // Sélectionner ce chat virtuel
          actions.selectChat(virtualChat as any);

          console.log(
            "Virtual chat prepared for:",
            locationState.startChatWith!.username
          );

          // Nettoyer l'état de navigation immédiatement
          navigate("/chat", { replace: true });
        } catch (error) {
          console.error("Error preparing chat interface:", error);
          hasInitializedFromNavigation.current = false; // Permettre de réessayer en cas d'erreur
        }
      };

      prepareChat();
    }
  }, [
    location.state,
    state.auth.currentUser?.id,
    state.auth.isAuthenticated,
    actions,
    navigate,
  ]);

  // Initialiser l'ID utilisateur et charger les chats au démarrage
  useEffect(() => {
    if (state.auth.currentUser?.id && state.auth.isAuthenticated) {
      actions.setCurrentUserId(state.auth.currentUser.id);

      // Connecter le WebSocket
      const token = localStorage.getItem("token");
      if (token) {
        actions.connectWebSocket(token);
      }

      // Charger les chats
      actions.loadChatsForCurrentUser();
    }

    // Nettoyage à la déconnexion du composant
    return () => {
      actions.disconnectWebSocket();
    };
  }, [state.auth.currentUser?.id, state.auth.isAuthenticated]); // Simplified dependencies

  // Auto-select first chat when chats are loaded (pour éviter que les messages disparaissent après actualisation)
  useEffect(() => {
    // Ne pas auto-sélectionner si on a déjà un chat actif ou si on est en train d'initialiser depuis la navigation
    const locationState = location.state as {
      startChatWith?: UserSearchResult;
    };
    if (locationState?.startChatWith || hasInitializedFromNavigation.current) {
      return; // Laisser l'effet précédent gérer l'initialisation
    }

    if (state.chats.length > 0 && !state.activeChat && !state.isLoading) {
      // Essayer de restaurer le chat actif depuis localStorage
      const savedActiveChatId = localStorage.getItem("activeChatId");
      let chatToSelect = state.chats[0]; // par défaut, sélectionner le premier

      if (savedActiveChatId) {
        const savedChat = state.chats.find(
          (chat: any) => chat.id === savedActiveChatId
        );
        if (savedChat) {
          chatToSelect = savedChat;
        }
      }

      actions.selectChat(chatToSelect);
    }
  }, [state.chats, state.activeChat, state.isLoading]);

  // Sauvegarder le chat actif dans localStorage
  useEffect(() => {
    if (state.activeChat) {
      localStorage.setItem("activeChatId", state.activeChat.id);
    }
  }, [state.activeChat]);

  // Scroll automatique vers le bas quand de nouveaux messages arrivent
  const activeMessages = state.activeChat
    ? state.messages[state.activeChat.id] || []
    : [];

  // Fonctions utilitaires refactorisées
  const getMessageMinute = (date: Date): number => {
    return date.getHours() * 60 + date.getMinutes();
  };

  const areMessagesInSameGroup = (msg1: any, msg2: any): boolean => {
    if (!msg1 || !msg2) return false;
    if (msg1.senderId !== msg2.senderId) return false;

    const minute1 = getMessageMinute(new Date(msg1.createdAt));
    const minute2 = getMessageMinute(new Date(msg2.createdAt));

    return minute1 === minute2;
  };

  const shouldShowTime = (
    message: any,
    index: number,
    messages: any[]
  ): boolean => {
    if (index === messages.length - 1) return true;
    const nextMessage = messages[index + 1];
    return !areMessagesInSameGroup(message, nextMessage);
  };

  const shouldShowAvatar = (
    message: any,
    index: number,
    messages: any[]
  ): boolean => {
    if (index === messages.length - 1) return true;
    const nextMessage = messages[index + 1];
    return !areMessagesInSameGroup(message, nextMessage);
  };

  const isMessageGrouped = (
    message: any,
    index: number,
    messages: any[]
  ): boolean => {
    const nextMessage = messages[index + 1];
    return areMessagesInSameGroup(message, nextMessage);
  };

  // Fonction pour déclencher le scroll vers le bas
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Fonction pour scroller au premier message non lu
  const scrollToFirstUnreadMessage = (
    messages: any[],
    currentUserId: string
  ) => {
    if (!messages || messages.length === 0) return;

    // Trouver le premier message non lu pour l'utilisateur actuel
    const firstUnreadIndex = messages.findIndex(
      (msg) => msg.receiverId === currentUserId && !msg.status.isRead()
    );

    if (firstUnreadIndex !== -1 && unreadMessageRef.current) {
      // Scroller au premier message non lu
      unreadMessageRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    } else {
      // Si aucun message non lu, scroller vers le bas
      scrollToBottom();
    }
  };

  // Déterminer si on doit scroller au message non lu ou vers le bas
  const handleChatScroll = (
    chatId: string,
    messages: any[],
    currentUserId: string
  ) => {
    const isFirstOpen = !chatFirstOpenStates[chatId];

    if (isFirstOpen) {
      // Première ouverture : scroller au premier message non lu
      scrollToFirstUnreadMessage(messages, currentUserId);
      setChatFirstOpenStates((prev) => ({ ...prev, [chatId]: true }));
    } else {
      // Ouvertures suivantes : scroller vers le bas
      scrollToBottom();
    }
  };

  // Composant pour le statut en ligne
  const OnlineStatus: React.FC<{ chatId: string; currentUserId: string }> = ({
    chatId,
    currentUserId,
  }) => {
    const chat = state.chats.find((c: any) => c.id === chatId);
    if (!chat) return null;

    const otherParticipant = chat.getOtherParticipant(currentUserId);
    const isOtherUserOnline = otherParticipant
      ? state.users[otherParticipant.id]?.isOnline ?? false
      : false;

    return (
      <div className="flex items-center space-x-2">
        <div
          className={`w-3 h-3 rounded-full ${
            isOtherUserOnline ? "bg-green-400 shadow-lg" : "bg-gray-500"
          } relative`}
        >
          {isOtherUserOnline && (
            <div className="absolute inset-0 bg-green-400 rounded-full animate-ping opacity-40"></div>
          )}
        </div>
        <span
          className={`text-sm font-medium ${
            isOtherUserOnline ? "text-green-300" : "text-black/60"
          }`}
        >
          {isOtherUserOnline ? "Online" : "Offline"}
        </span>
      </div>
    );
  };

  // Composant pour la liste des messages avec indicateur de frappe
  const MessagesList: React.FC<{ messages: any[]; currentUserId: string }> = ({
    messages,
    currentUserId,
  }) => {
    if (messages.length === 0) {
      return (
        <div
          className="text-center p-8 rounded-xl backdrop-blur-sm"
          style={{
            background: `linear-gradient(135deg, 
              rgba(255,255,255,0.05) 0%, 
              rgba(255,255,255,0.02) 50%, 
              rgba(255,255,255,0.05) 100%
            )`,
            border: "1px solid rgba(255,255,255,0.1)",
          }}
        >
          <div className="relative mb-4">
            <div className="w-12 h-12 mx-auto bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full flex items-center justify-center">
              <span className="text-2xl opacity-50">💬</span>
            </div>
          </div>
          <p className="text-black/60">No messages in this conversation</p>
          <p className="text-black/40 text-sm mt-2">Start the discussion!</p>
        </div>
      );
    }

    // Obtenir les utilisateurs qui tapent dans le chat actuel
    const typingUsers = state.activeChat
      ? Array.from(state.typingUsers[state.activeChat.id] || new Set()).filter(
          (userId: any) => userId !== currentUserId
        )
      : [];

    const userNames = Object.keys(state.users).reduce((acc, userId) => {
      acc[userId] = state.users[userId].name;
      return acc;
    }, {} as Record<string, string>);

    // Trouver l'index du premier message non lu
    const firstUnreadIndex = messages.findIndex(
      (msg) => msg.receiverId === currentUserId && !msg.status.isRead()
    );

    return (
      <>
        {messages.map((message, index) => (
          <div key={`message-container-${message.id}`}>
            {/* Indicateur de nouveaux messages - style sombre */}
            {index === firstUnreadIndex && firstUnreadIndex !== -1 && (
              <div
                ref={unreadMessageRef}
                className={`flex items-center gap-3 my-6 px-4 transition-all duration-500 ease-out transform ${
                  isUnreadIndicatorVisible
                    ? "opacity-100 translate-y-0 scale-100"
                    : "opacity-0 -translate-y-2 scale-95"
                }`}
              >
                <div className="flex-1 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent transition-all duration-500 ease-out"></div>
                <span
                  className="text-xs text-red-300 font-semibold px-3 py-1 rounded-full backdrop-blur-sm transition-all duration-500 ease-out"
                  style={{
                    background: `linear-gradient(135deg, 
                      rgba(239, 68, 68, 0.2) 0%, 
                      rgba(220, 38, 127, 0.1) 50%, 
                      rgba(239, 68, 68, 0.2) 100%
                    )`,
                    border: "1px solid rgba(239, 68, 68, 0.3)",
                  }}
                >
                  New messages
                </span>
                <div className="flex-1 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent transition-all duration-500 ease-out"></div>
              </div>
            )}
            <ChatMessage
              message={message}
              sender={state.users[message.senderId]}
              isFromCurrentUser={message.senderId === currentUserId}
              showTime={shouldShowTime(message, index, messages)}
              showAvatar={shouldShowAvatar(message, index, messages)}
              isGrouped={isMessageGrouped(message, index, messages)}
              onImageLoad={scrollToBottom}
            />
          </div>
        ))}
        {/* Indicateur de frappe */}
        <TypingIndicator typingUsers={typingUsers} userNames={userNames} />
        <div ref={messagesEndRef} />
      </>
    );
  };

  // Réinitialiser le statut de première ouverture quand on change de chat actif
  useEffect(() => {
    if (state.activeChat && state.auth.currentUser?.id) {
      const currentUserId = state.auth.currentUser.id;
      const activeChatId = state.activeChat.id;
      const messages = state.messages[activeChatId] || [];

      // Si le chat a des messages non lus, s'assurer qu'il est marqué comme "première ouverture"
      const hasUnreadMessages = messages.some(
        (msg) => msg.receiverId === currentUserId && !msg.status.isRead()
      );

      if (hasUnreadMessages && chatFirstOpenStates[activeChatId]) {
        setChatFirstOpenStates((prev) => ({ ...prev, [activeChatId]: false }));
      }

      // Réinitialiser la visibilité de l'indicateur
      setIsUnreadIndicatorVisible(true);
    }
  }, [state.activeChat?.id, activeMessages]);

  // Gérer le scroll quand les messages changent
  useEffect(() => {
    if (
      state.activeChat &&
      activeMessages.length > 0 &&
      state.auth.currentUser?.id
    ) {
      handleChatScroll(
        state.activeChat.id,
        activeMessages,
        state.auth.currentUser.id
      );
    }
  }, [activeMessages, state.activeChat?.id]);

  // Ne pas afficher le chat si l'utilisateur n'est pas connecté
  if (!state.auth.isAuthenticated) {
    return (
      <div className="flex items-center justify-center p-8">
        <div
          className="w-96 p-8 rounded-2xl backdrop-blur-xl shadow-2xl"
          style={{
            background: `linear-gradient(135deg, 
              rgba(239, 68, 68, 0.1) 0%, 
              rgba(220, 38, 127, 0.05) 50%, 
              rgba(239, 68, 68, 0.1) 100%
            )`,
            backdropFilter: "blur(20px)",
            border: "1px solid rgba(239, 68, 68, 0.2)",
          }}
        >
          <div className="text-center">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center shadow-xl mb-4">
              <span className="text-2xl">🔒</span>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              Login required
            </h3>
            <p className="text-white/70">
              You must be logged in to use the chat
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Vérifier que l'utilisateur a un ID valide
  if (!state.auth.currentUser?.id) {
    return (
      <div className="flex items-center justify-center p-8">
        <div
          className="w-96 p-8 rounded-2xl backdrop-blur-xl shadow-2xl"
          style={{
            background: `linear-gradient(135deg, 
              rgba(255, 165, 0, 0.1) 0%, 
              rgba(255, 140, 0, 0.05) 50%, 
              rgba(255, 165, 0, 0.1) 100%
            )`,
            backdropFilter: "blur(20px)",
            border: "1px solid rgba(255, 165, 0, 0.2)",
          }}
        >
          <div className="text-center">
            <div className="w-16 h-16 mx-auto bg-gradient-to-br from-orange-500 to-yellow-600 rounded-full flex items-center justify-center shadow-xl mb-4">
              <span className="text-2xl">⚠️</span>
            </div>
            <h3 className="text-lg font-semibold text-black mb-2">
              Invalid user session
            </h3>
            <p className="text-white/70">
              Please refresh the page or log in again
            </p>
          </div>
        </div>
      </div>
    );
  }

  const currentUserId = state.auth.currentUser.id;

  // Fonctions de gestion d'événements
  const handleSendMessage = async (
    content: string,
    mediaList?: PendingMedia[]
  ) => {
    if (!state.activeChat) return;

    const otherParticipant =
      state.activeChat.getOtherParticipant(currentUserId);
    if (!otherParticipant) return;

    try {
      // Vérifier si c'est un chat virtuel (premier message)
      const isVirtualChat = (state.activeChat as any).isVirtual;

      if (isVirtualChat) {
        console.log("First message in virtual chat - creating real chat");

        // Créer le vrai chat en base de données
        const realChat = await actions.selectUserForChat({
          id: otherParticipant.getId(),
          username: otherParticipant.getName(),
          email: otherParticipant.getEmail(),
          avatar: otherParticipant.getAvatar(),
          isOnline: otherParticipant.isUserOnline(),
        });

        // Le chat réel est maintenant sélectionné, continuer avec l'envoi du message
      }

      // Si on a des médias avec des captions, envoyer chaque média avec sa caption
      if (mediaList && mediaList.length > 0) {
        for (const media of mediaList) {
          const caption = media.caption || content.trim();

          if (media.type === "image") {
            await actions.uploadImage(
              media.file,
              otherParticipant.getId(),
              caption
            );
          } else if (media.type === "video") {
            await actions.uploadVideo(
              media.file,
              otherParticipant.getId(),
              caption
            );
          }
        }
      } else if (content.trim()) {
        // Si pas de médias mais du contenu texte, envoyer un message texte normal
        await actions.sendMessageAsCurrentUser(
          content,
          otherParticipant.getId()
        );
      }

      await loadUserInfos();
    } catch (error) {
      console.error("Error while sending the message:", error);
      toast.error("Error while sending the message, verify your credits");
    }
  };

  const handleSelectUser = async (selectedUser: UserSearchResult) => {
    try {
      await actions.selectUserForChat(selectedUser);
    } catch (error) {
      console.error("Error while selecting user for chat:", error);
    }
  };

  const handleStartTyping = (chatId: string, receiverId: string) => {
    actions.startTyping(chatId, receiverId);
  };

  const handleStopTyping = (chatId: string, receiverId: string) => {
    actions.stopTyping(chatId, receiverId);
  };

  // Gérer le focus sur l'input pour marquer les messages comme lus
  const handleInputFocus = async () => {
    if (!state.activeChat || !state.auth.currentUser?.id) return;

    const currentUserId = state.auth.currentUser.id;
    const activeChatId = state.activeChat.id;
    const messages = state.messages[activeChatId] || [];

    // Trouver tous les messages non lus pour l'utilisateur actuel
    const unreadMessages = messages.filter(
      (msg) => msg.receiverId === currentUserId && !msg.status.isRead()
    );

    if (unreadMessages.length === 0) return;

    // Déclencher l'animation de disparition
    setIsUnreadIndicatorVisible(false);

    // Attendre que l'animation se termine avant de marquer comme lus
    setTimeout(() => {
      // Mettre à jour le state local pour faire disparaître l'indicateur
      actions.markAllUnreadAsReadLocally(activeChatId, currentUserId);

      // Marquer tous les messages non lus comme lus côté serveur (en arrière-plan)
      unreadMessages.forEach(async (message) => {
        try {
          await actions.markMessageAsRead(message.id);
        } catch (error) {
          console.error(`Failed to mark message ${message.id} as read:`, error);
        }
      });
    }, 500); // Correspond à la durée de l'animation CSS
  };

  return (
    <>
      <div className="mt-12 lg:mt-0 flex flex-col lg:flex-row h-full min-h-0 relative">
        {/* Sidebar avec les chats - Style glassmorphique */}
        <div
          className="w-full lg:w-80 flex flex-col bg-white backdrop-blur-xl shadow-2xl relative"
          // style={{
          //   background: `linear-gradient(135deg,
          //     rgba(255,255,255,0.1) 0%,
          //     rgba(255,255,255,0.05) 50%,
          //     rgba(255,255,255,0.1) 100%
          //   )`,
          //   backdropFilter: "blur(20px)",
          //   borderRight: "1px solid rgba(255,255,255,0.1)",
          // }}
        >
          {/* Header avec effet brillant */}
          <div className="flex-shrink-0 p-6 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-pink-500/10 via-purple-500/10 to-pink-500/10 blur-sm"></div>
            <div className="relative flex items-center justify-between">
              <h2 className="text-xl font-bold bg-gradient-to-r from-purple-500 via-pink-400 to-pink-500 bg-clip-text text-transparent">
                Conversations
              </h2>
              <Button
                size="sm"
                disabled={!userInfos?.canSendMessage}
                onClick={() => setIsUserSearchModalOpen(true)}
                className="h-10 w-10 rounded-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-blue-400 hover:to-purple-500 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
              >
                <span className="text-lg font-bold">+</span>
              </Button>
            </div>
          </div>

          {/* Liste des conversations */}
          <div className="flex-1 overflow-y-auto px-3 pb-3">
            {state.isLoading ? (
              <div className="p-6 text-center">
                <div className="animate-spin w-8 h-8 border-2 border-white/20 border-t-blue-400 rounded-full mx-auto mb-3"></div>
                <p className="text-black/70">Loading...</p>
              </div>
            ) : state.chats.length === 0 ? (
              <div
                className="p-6 text-center rounded-xl mx-2 backdrop-blur-sm"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(59, 130, 246, 0.1) 0%, 
                    rgba(147, 51, 234, 0.05) 50%, 
                    rgba(59, 130, 246, 0.1) 100%
                  )`,
                  border: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                <p className="text-black/70">No conversation</p>
              </div>
            ) : (
              <div className="space-y-2">
                {state.chats
                  .filter((chat) => !(chat as any).isVirtual)
                  .map((chat) => (
                    <Button
                      key={chat.id}
                      variant="ghost"
                      className={`w-full justify-start h-auto p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] ${
                        state.activeChat?.id === chat.id
                          ? "bg-gradient-to-r from-pink-500/20 to-purple-500/20 backdrop-blur-sm border border-pink-400/30 text-black shadow-lg"
                          : "text-black/90 bg-black/10 hover:text-black hover:bg-black/10 border border-transparent hover:border-black/20 backdrop-blur-sm"
                      }`}
                      onClick={() => actions.selectChat(chat)}
                      disabled={
                        chat.getBlockedStatus(currentUserId) ===
                        UserBlockStatus.BLOCKED
                      }
                    >
                      <div className="flex flex-col items-start w-full">
                        <span className="font-semibold text-left drop-shadow-sm">
                          {chat.getDisplayName(currentUserId)}
                        </span>

                        {!userInfos?.canViewMessages ? (
                          <span className="text-sm text-left text-red-500">
                            You need at least 5 credits to view this conversation
                          </span>
                        ) : (
                          <>
                            {chat.lastMessage?.getType().getValue() ===
                            MessageTypeEnum.IMAGE ? (
                              <span className="text-xs text-black/60 truncate max-w-full text-left mt-1">
                                {chat.lastMessage.senderId === currentUserId
                                  ? "You"
                                  : state.users[chat.lastMessage.senderId]
                                      ?.name}{" "}
                                : sent an image
                              </span>
                            ) : chat.lastMessage?.getType().getValue() ===
                              MessageTypeEnum.VIDEO ? (
                              <span className="text-xs text-black/60 truncate max-w-full text-left mt-1">
                                {chat.lastMessage.senderId === currentUserId
                                  ? "You"
                                  : state.users[chat.lastMessage.senderId]
                                      ?.name}{" "}
                                : sent a video
                              </span>
                            ) : chat.lastMessage?.getType().getValue() ===
                              MessageTypeEnum.TEXT ? (
                              <span className="text-xs text-black/60 truncate max-w-full text-left mt-1">
                                {chat.lastMessage.senderId === currentUserId
                                  ? "You"
                                  : state.users[chat.lastMessage.senderId]
                                      ?.name}{" "}
                                : {chat.lastMessage.content.getPreview(50)}
                              </span>
                            ) : null}
                          </>
                        )}
                      </div>
                    </Button>
                  ))}
              </div>
            )}
          </div>
        </div>

        {/* Zone de chat principale - Style glassmorphique */}
        <div className="flex-1 flex flex-col min-h-0 relative">
          {state.activeChat ? (
            <>
              {/* Header du chat */}
              <div
                className="flex-shrink-0 bg-white p-6 backdrop-blur-xl"
                // style={{
                //   background: `linear-gradient(135deg,
                //     rgba(255,255,255,0.08) 0%,
                //     rgba(255,255,255,0.04) 50%,
                //     rgba(255,255,255,0.08) 100%
                //   )`,
                //   backdropFilter: "blur(20px)",
                //   borderBottom: "1px solid rgba(255,255,255,0.1)",
                // }}
              >
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/10 via-purple-400/10 to-pink-400/10 blur-sm rounded-lg"></div>
                  <div className="relative">
                    <h3 className="text-xl font-bold text-purple-500 drop-shadow-sm mb-2">
                      {state.activeChat.getDisplayName(currentUserId)}
                    </h3>
                    <div className="flex items-center gap-2">
                      <OnlineStatus
                        chatId={state.activeChat.id}
                        currentUserId={currentUserId}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Messages avec arrière-plan subtil */}
              <div
                className="flex-1 relative overflow-y-auto p-6 min-h-0"
                style={{
                  background: `linear-gradient(180deg, 
                    rgba(255,255,255,0.02) 0%, 
                    rgba(255,255,255,0.01) 50%, 
                    rgba(255,255,255,0.02) 100%
                  )`,
                }}
              >
                <div className="space-y-2">
                  {!userInfos?.canViewMessages ? (
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 overflow-hidden opacity-70 p-6 text-center rounded-xl mx-2 backdrop-blur-sm">
                      <p className="text-black/70 text-lg">
                        You need at least{" "}
                        <span className="font-bold">5 credits</span> to view this
                        conversation
                      </p>
                      <p className="text-black/70 text-lg">
                        Please top up your account
                      </p>
                      <Button
                        onClick={() => navigate("/booking")}
                        className="bg-pink-600 hover:bg-pink-700 text-white border-0 mt-4 text-xl"
                      >
                        Go to booking page
                      </Button>
                    </div>
                  ) : (
                    <MessagesList
                      messages={activeMessages}
                      currentUserId={currentUserId}
                    />
                  )}
                </div>
              </div>

              {/* Input pour envoyer des messages */}
              <div
                className="flex-shrink-0 bg-white backdrop-blur-xl"
                // style={{
                //   background: `linear-gradient(135deg,
                //     rgba(255,255,255,0.08) 0%,
                //     rgba(255,255,255,0.04) 50%,
                //     rgba(255,255,255,0.08) 100%
                //   )`,
                //   backdropFilter: "blur(20px)",
                //   borderTop: "1px solid rgba(255,255,255,0.1)",
                // }}
              >
                {
                  !userInfos?.canSendMessage ? (
                    <div className="p-4 text-center text-red-500">
                      You need at least <span className="font-bold">15 credits</span> to send messages
                    </div>
                  ) : null
                }
                <MessageInput
                  onSendMessage={handleSendMessage}
                  onStartTyping={handleStartTyping}
                  onStopTyping={handleStopTyping}
                  onFocus={handleInputFocus}
                  chatId={state.activeChat.id}
                  receiverId={
                    state.activeChat.getOtherParticipant(currentUserId)?.id
                  }
                  disabled={!state.isConnected || !userInfos?.canSendMessage}
                  editingMessage={state.editingMessage}
                  onSaveEdit={actions.saveEditedMessage}
                  onCancelEdit={actions.cancelEditingMessage}
                />
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center p-8">
              <div
                className="w-96 p-8 rounded-2xl backdrop-blur-xl shadow-2xl"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(255,255,255,0.1) 0%, 
                    rgba(255,255,255,0.05) 50%, 
                    rgba(255,255,255,0.1) 100%
                  )`,
                  backdropFilter: "blur(20px)",
                  border: "1px solid rgba(255,255,255,0.1)",
                }}
              >
                <div className="text-center">
                  {/* Icône décorative */}
                  <div className="relative mb-6">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-xl">
                      <span className="text-2xl">💬</span>
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full blur-md opacity-30 scale-110"></div>
                  </div>

                  <h3 className="text-xl font-bold text-black mb-4 drop-shadow-sm">
                    Welcome Toodiscreet Chat
                  </h3>
                  <p className="text-black/70 mb-8 leading-relaxed">
                    Select an existing conversation or create a new one to start
                    chatting
                  </p>

                  <div className="space-y-4">
                    <Button
                      disabled={!userInfos?.canSendMessage}
                      className="w-full bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                      onClick={() => setIsUserSearchModalOpen(true)}
                    >
                      New conversation
                    </Button>
                    <p className="text-xs text-white/50">
                      Or use the sidebar to select an existing conversation
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Affichage des erreurs */}
          {state.error && (
            <div
              className="p-4 backdrop-blur-sm"
              style={{
                background: `linear-gradient(135deg, 
                  rgba(239, 68, 68, 0.1) 0%, 
                  rgba(220, 38, 127, 0.05) 50%, 
                  rgba(239, 68, 68, 0.1) 100%
                )`,
                borderTop: "1px solid rgba(239, 68, 68, 0.2)",
              }}
            >
              <p className="text-sm text-red-300">{state.error}</p>
            </div>
          )}
        </div>
      </div>

      {/* Modal de recherche d'utilisateurs */}
      <UserSearchModal
        isOpen={isUserSearchModalOpen}
        onClose={() => setIsUserSearchModalOpen(false)}
        onSelectUser={handleSelectUser}
      />
    </>
  );
};

// Component principal qui fournit les dépendances
const ChatPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { searchUsersUseCase } = useUserSearch();

  // Créer les dépendances à injecter
  const authDependency: AuthDependency = useMemo(
    () => ({
      currentUser: user
        ? {
            id: user.id,
            email: user.email.getValue(),
            username: user.username.getValue(),
          }
        : null,
      isAuthenticated,
    }),
    [user, isAuthenticated]
  );

  const userSearchDependency: UserSearchDependency = useMemo(
    () => ({
      searchUsers: async (term: string): Promise<UserSearchResult[]> => {
        try {
          // Utiliser UserApiClient pour la recherche
          const response = await searchUsersUseCase.execute({
            searchTerm: term,
          });

          if (!response.users) {
            return [];
          }

          // Mapper les résultats au format attendu par ChatContext
          const searchResults: UserSearchResult[] = response.users.map(
            (user) => ({
              id: user.id,
              username: user.username,
              email: user.email,
              avatar: user.avatar,
              isOnline: user.isOnline || false,
            })
          );

          return searchResults;
        } catch (error) {
          console.error("Erreur lors de la recherche d'utilisateurs:", error);
          return [];
        }
      },
    }),
    [searchUsersUseCase]
  );

  return (
    <ChatProvider auth={authDependency} userSearch={userSearchDependency}>
      <ChatPageContent />
    </ChatProvider>
  );
};

export default ChatPage;
