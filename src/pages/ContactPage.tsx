import AuthNav from "@/components/AuthNav";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/Textarea";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { Card, Input } from "@/modules/shared";
import { ArrowRightIcon, Home, Mail, MapPin } from "lucide-react";
import { useState } from "react";

const ContactPage = () => {
  const { contactUs, sendingEmail } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    lastname: "",
    email: "",
    message: "",
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Handle form submission
    await contactUs(formData);
    setFormData({
      name: "",
      lastname: "",
      email: "",
      message: "",
    });
  };

  return (
    <div className="relative min-h-screen">
      <AuthNav />
      <div className="absolute inset-0 bg-gradient-to-tr from-purple-600/10 via-red-500/10 to-pink-500/10 -z-10"></div>
      <div className="container flex items-center justify-center min-h-[calc(100vh-4rem)] py-12 px-6">
        <Card className="w-full lg:w-4/5 bg-white backdrop-blur-xl shadow-2xl p-6 mx-auto">
          <div className="flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
            <div className="flex-1">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-red-400 to-pink-500 bg-clip-text text-transparent flex items-center space-x-3">
                <Mail className="h-8 w-8 text-pink-400" />
                <span>Contact Us</span>
              </h2>
              <p className="text-lg text-black/70">We are here to help you.</p>
              <div className="space-y-2 text-gray-600 mt-12 text-sm">
                <div className="flex items-center space-x-2">
                  <Home size={16} />
                  <span>Keytech Partners SIA</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Mail size={16} />
                  <span>Registration number 40203645640</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin size={16} />
                  <span> 7, Tapesu iela, LV-1083, Riga, Latvia</span>
                </div>
              </div>
            </div>
            <div className="flex-1">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-red-400 to-pink-500 bg-clip-text text-transparent flex items-center space-x-3">
                <span>Send us a message</span>
              </h2>
              <p className="text-lg text-black/70">
                We will get back to you as soon as possible.
              </p>

              <form onSubmit={handleSubmit} className="space-y-6 mt-12">
                <div className="space-y-5">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="relative">
                      <Input
                        label="First Name"
                        type="text"
                        value={formData.name}
                        onChange={(e) =>
                          handleInputChange("name", e.target.value)
                        }
                        placeholder="Your first name"
                        required
                      />
                    </div>
                    <div className="relative">
                      <Input
                        label="Last Name"
                        type="text"
                        value={formData.lastname}
                        onChange={(e) =>
                          handleInputChange("lastname", e.target.value)
                        }
                        placeholder="Your last name"
                        required
                      />
                    </div>
                  </div>
                  <div className="relative">
                    <Input
                      label="Email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      placeholder="Your email address"
                      required
                    />
                  </div>
                  <div className="relative">
                    <Textarea
                      label="Message"
                      rows={4}
                      value={formData.message}
                      onChange={(e) =>
                        handleInputChange("message", e.target.value)
                      }
                      placeholder="Your message"
                      required
                    />
                  </div>
                </div>
                <button
                  type="submit"
                  className="w-full md:w-auto flex bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 items-center justify-center space-x-2 rounded-full"
                >
                  {sendingEmail ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-3 w-3 border-2 border-white/30 border-t-white"></div>
                      <span className="text-sm text-white">Sending...</span>
                    </div>
                  ) : (
                    <span className="flex items-center gap-2">
                      Send
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </span>
                  )}
                </button>
              </form>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ContactPage;
