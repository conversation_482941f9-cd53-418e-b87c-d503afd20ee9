import React, { useState } from 'react';
import { useAuth } from '@/modules/auth/presentation/hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { User } from '@/modules/users/domain/entities/User';
import { UserSearchModal } from '@/modules/chat/presentation/components/UserSearchModal';
import { UserSearchResult } from '@/modules/chat/presentation/context/types/ChatTypes';
import { ChatLayout } from '../components/ChatLayout';
import { UserSuggestions } from '../components/UserSuggestions';

const HomePage: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [isUserSearchModalOpen, setIsUserSearchModalOpen] = useState(false);

  const handleStartChat = (selectedUser: User) => {
    // Convertir User en UserSearchResult pour compatibilité
    const userSearchResult: UserSearchResult = {
      id: selectedUser.id,
      username: selectedUser.username,
      email: selectedUser.email,
      avatar: selectedUser.avatar,
      isOnline: selectedUser.isOnline || false
    };

    // Naviguer directement vers la page de chat avec l'utilisateur sélectionné
    navigate('/chat', { 
      state: { 
        startChatWith: userSearchResult 
      } 
    });
  };

  const handleSelectUser = async (user: UserSearchResult): Promise<void> => {
    // Naviguer vers la messagerie et démarrer une conversation
    navigate('/chat', { 
      state: { 
        startChatWith: user 
      } 
    });
  };

  return (
    <ChatLayout className="bg-white/10 overflow-y-auto">
      <div className="max-w-7xl mx-auto flex flex-col">
        {/* Message de bienvenue */}
        <div className="mb-8 mt-12 lg:mt-0">
          <h1 className="text-4xl font-bold mb-2 text-black">
            Hello {user?.username?.getValue()} ! 👋
          </h1>
          <p className="text-lg text-black/70">
            Discover and connect with new people on Toodiscreet
          </p>
        </div>

        {/* Suggestions d'utilisateurs */}
        <UserSuggestions onStartChat={handleStartChat} />
      </div>

      {/* Modal de recherche d'utilisateurs */}
      <UserSearchModal
        isOpen={isUserSearchModalOpen}
        onClose={() => setIsUserSearchModalOpen(false)}
        onSelectUser={handleSelectUser}
      />
    </ChatLayout>
  );
};

export default HomePage;
