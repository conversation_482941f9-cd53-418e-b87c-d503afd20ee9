import { ChatLayout } from "@/components/ChatLayout";
import { Card } from "@/modules/shared";
import {
  CheckCircle,
  Download,
  Mail,
  CreditCard,
  Calendar,
  User,
  EuroIcon,
  ArrowRight,
  Home,
  Receipt,
} from "lucide-react";
import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
} from "@react-pdf/renderer";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    fontSize: 12,
    fontFamily: "Helvetica",
  },
  section: {
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    marginBottom: 10,
    fontWeight: "bold",
  },
});

function SuccessPaymentPage() {
  const { state } = useLocation();
  const navigate = useNavigate();

  // Extract payment data with fallbacks
  const paymentData = state || {};
  const {
    clientName,
    clientEmail,
    plan,
    customAmount,
    price,
    credits,
    clientCardNumber,
    clientCardExpirationDate,
    transactionId = `TXN-${Date.now()}`,
    paymentDate = new Date().toLocaleDateString(),
  } = paymentData;

  const finalAmount = plan === "free" ? customAmount : price;
  const maskedCardNumber = clientCardNumber
    ? `**** **** **** ${clientCardNumber.slice(-4)}`
    : "**** **** **** 4242";

  // PDF document component
  const ReceiptPDF = ({
    transactionId,
    paymentDate,
    plan,
    credits,
    finalAmount,
    clientName,
    clientEmail,
    maskedCardNumber,
    clientCardExpirationDate,
  }: any) => (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Reçu de Paiement</Text>
          <Text>Transaction ID: {transactionId}</Text>
          <Text>Payment Date: {paymentDate}</Text>
          <Text>Plan: {plan}</Text>
          <Text>Credits Included: {credits}</Text>
          <Text>Amount Paid: {finalAmount} €</Text>
          <Text>Customer Name: {clientName}</Text>
          <Text>Email Address: {clientEmail}</Text>
          <Text>Payment Method: Credit Card</Text>
          <Text>Card Number: {maskedCardNumber}</Text>
          <Text>Expiration Date: {clientCardExpirationDate}</Text>
        </View>
      </Page>
    </Document>
  );

  //   const downloadReceipt = async () => {
  //     const blob = await pdf(
  //       <ReceiptPDF
  //         transactionId={transactionId}
  //         paymentDate={paymentDate}
  //         plan={plan}
  //         credits={credits}
  //         finalAmount={finalAmount}
  //         clientName={clientName}
  //         clientEmail={clientEmail}
  //         maskedCardNumber={maskedCardNumber}
  //         clientCardExpirationDate={clientCardExpirationDate}
  //       />
  //     ).toBlob();

  //     const url = URL.createObjectURL(blob);
  //     const link = document.createElement("a");
  //     link.href = url;
  //     link.download = "payment_receipt.pdf";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     URL.revokeObjectURL(url);
  //   };

  const downloadReceipt = () => {
    const receiptHtml = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Payment Receipt</title>
        <style>
          body { 
            font-family: 'Arial', sans-serif; 
            margin: 0; 
            padding: 15px; 
            color: #333;
            line-height: 1.4;
            font-size: 14px;
          }
          .header {
            text-align: center;
            border-bottom: 2px solid #8B5CF6;
            padding-bottom: 15px;
            margin-bottom: 20px;
          }
          .header h1 {
            color: #8B5CF6;
            margin: 0 0 5px 0;
            font-size: 22px;
            font-weight: bold;
          }
          .header p {
            color: #666;
            margin: 3px 0;
            font-size: 13px;
          }
          .content {
            max-width: 550px;
            margin: 0 auto;
          }
          .section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 6px;
            border-left: 3px solid #8B5CF6;
          }
          .section h3 {
            color: #8B5CF6;
            margin: 0 0 12px 0;
            font-size: 16px;
          }
          .info-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 3px 0;
          }
          .info-label {
            font-weight: 600;
            color: #4B5563;
            font-size: 13px;
          }
          .info-value {
            color: #1F2937;
            font-size: 13px;
          }
          .amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
          }
          .transaction-id {
            font-family: 'Courier New', monospace;
            background: #E5E7EB;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
          }
          .footer {
            text-align: center;
            border-top: 1px solid #E5E7EB;
            padding-top: 15px;
            margin-top: 30px;
            color: #6B7280;
            font-size: 11px;
            line-height: 1.3;
          }
          .footer h4 {
            color: #8B5CF6;
            margin: 0 0 8px 0;
            font-size: 13px;
          }
        .logo{
            font-size: 24px;
            font-weight: bold;
            color: #8B5CF6;
            margin-top: 16px;
          }
        }
          @media print {
            body { 
              padding: 10px;
              font-size: 13px;
            }
            .header, .section { 
              break-inside: avoid; 
            }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>🧾 PAYMENT RECEIPT</h1>
          <p>Transaction processed successfully</p>
          <p>${new Date().toLocaleString()}</p>
        </div>

        <div class="content">
          <div class="section">
            <h3>📋 Transaction Details</h3>
            <div class="info-row">
              <span class="info-label">Transaction ID:</span>
              <span class="info-value transaction-id">${transactionId}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Payment Date:</span>
              <span class="info-value">${paymentDate}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Status:</span>
              <span class="info-value" style="color: #059669; font-weight: 600;">✅ COMPLETED</span>
            </div>
          </div>

          <div class="section">
            <h3>👤 Customer Information</h3>
            <div class="info-row">
              <span class="info-label">Name:</span>
              <span class="info-value">${clientName}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Email:</span>
              <span class="info-value">${clientEmail}</span>
            </div>
          </div>

          <div class="section">
            <h3>🎯 Purchase Details</h3>
            <div class="info-row">
              <span class="info-label">Plan:</span>
              <span class="info-value">${
                plan === "free"
                  ? "FREE ACCESS"
                  : plan === "select_24h"
                  ? "24h TO SELECT"
                  : plan === "discover_72h"
                  ? "72h TO DISCOVER"
                  : "96h TO CONCLUDE"
              }</span>
            </div>
            <div class="info-row">
              <span class="info-label">Credits:</span>
              <span class="info-value">${credits} credits</span>
            </div>
            <div class="info-row">
              <span class="info-label">Duration:</span>
              <span class="info-value">${
                plan === "free"
                  ? "Unlimited"
                  : plan === "select_24h"
                  ? "24 hours"
                  : plan === "discover_72h"
                  ? "72 hours"
                  : "96 hours"
              }</span>
            </div>
          </div>

          <div class="section">
            <h3>💳 Payment Information</h3>
            <div class="info-row">
              <span class="info-label">Payment Method:</span>
              <span class="info-value">Credit Card</span>
            </div>
            <div class="info-row">
              <span class="info-label">Card Number:</span>
              <span class="info-value">${maskedCardNumber}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Expiration:</span>
              <span class="info-value">${clientCardExpirationDate}</span>
            </div>
            <div class="info-row">
              <span class="info-label">Amount Paid:</span>
              <span class="info-value amount">€${finalAmount}</span>
            </div>
          </div>
        </div>

        <div class="footer">
          <h4>Thank you for your purchase!</h4>
          <p>Your access is now active and ready to use.</p>
          <p>Need help? Contact <EMAIL></p>
          <p>This receipt is valid as proof of payment.</p>
          <p style="margin-top: 10px; font-size: 10px;">
            © 2025 Your Company. All rights reserved.<br>
            Transaction processed securely.
          </p>
          <div class="logo">
            TooDiscreet
          </div>
        </div>
      </body>
      </html>
    `;

    // Create a new window and print
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(receiptHtml);
      printWindow.document.close();
      printWindow.onload = () => {
        printWindow.print();
        setTimeout(() => {
          printWindow.close();
        }, 100);
      };
    }
  };

  return (
    <ChatLayout className="p-6 min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Success Animation Header */}
        <div className="flex mt-12 lg:mt-0 items-center justify-center flex-col space-y-6">
          <div className="relative">
            <div className="absolute inset-0 bg-green-400 rounded-full blur-xl opacity-30 animate-pulse"></div>
            <CheckCircle className="h-24 w-24 text-green-500 relative z-10 animate-bounce" />
          </div>

          <h1 className="text-5xl font-bold bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 bg-clip-text text-transparent text-center">
            Payment Successful!
          </h1>

          <p className="text-xl text-gray-600 text-center max-w-2xl">
            Thank you for your purchase! Your payment has been processed
            successfully and you should receive a confirmation email shortly.
          </p>
        </div>

        {/* Payment Details Card */}
        <Card className="border-0 shadow-2xl backdrop-blur-xl bg-white/80 overflow-hidden">
          <div className="p-8 lg:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Transaction Summary */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <Receipt className="h-6 w-6 text-purple-600" />
                  <h3 className="text-2xl font-bold text-gray-800">
                    Transaction Details
                  </h3>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl border border-purple-100">
                    <span className="text-gray-600 font-medium">
                      Transaction ID
                    </span>
                    <span className="font-mono text-sm bg-white px-3 py-1 rounded-lg border text-gray-600">
                      {transactionId}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                    <span className="text-gray-600 font-medium">
                      Payment Date
                    </span>
                    <span className="font-semibold text-gray-800">
                      {paymentDate}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl border border-indigo-100">
                    <span className="text-gray-600 font-medium">Plan</span>
                    <div className="text-right">
                      <span className="font-semibold text-purple-600 capitalize block">
                        {plan === "free"
                          ? "FREE ACCESS"
                          : plan === "select_24h"
                          ? "24h TO SELECT"
                          : plan === "discover_72h"
                          ? "72h TO DISCOVER"
                          : "96h TO CONCLUDE"}
                      </span>
                      <span className="text-sm text-gray-500">
                        {plan === "free"
                          ? "Free Access"
                          : plan === "select_24h"
                          ? "Available for 24 hours"
                          : plan === "discover_72h"
                          ? "Available for 72 hours"
                          : "Available for 96 hours"}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                    <span className="text-gray-600 font-medium">
                      Credits Included
                    </span>
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-bold">C</span>
                      </div>
                      <span className="text-2xl font-bold text-purple-600">
                        {credits}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl border border-green-100">
                    <span className="text-gray-600 font-medium">
                      Amount Paid
                    </span>
                    <div className="flex items-center space-x-1">
                      <EuroIcon className="h-5 w-5 text-green-600" />
                      <span className="text-2xl font-bold text-green-600">
                        {finalAmount}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <User className="h-6 w-6 text-blue-600" />
                  <h3 className="text-2xl font-bold text-gray-800">
                    Customer Information
                  </h3>
                </div>

                <div className="space-y-4">
                  <div className="p-4 bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl border border-gray-100">
                    <div className="flex items-center space-x-3 mb-2">
                      <User className="h-5 w-5 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">
                        Customer Name
                      </span>
                    </div>
                    <p className="font-semibold text-gray-800 text-lg">
                      {clientName}
                    </p>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-slate-50 to-gray-50 rounded-xl border border-gray-100">
                    <div className="flex items-center space-x-3 mb-2">
                      <Mail className="h-5 w-5 text-gray-500" />
                      <span className="text-sm font-medium text-gray-600">
                        Email Address
                      </span>
                    </div>
                    <p className="font-semibold text-gray-800 text-lg">
                      {clientEmail}
                    </p>
                  </div>

                  <div className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl border border-blue-100">
                    <div className="flex items-center space-x-3 mb-2">
                      <CreditCard className="h-5 w-5 text-blue-500" />
                      <span className="text-sm font-medium text-gray-600">
                        Payment Method
                      </span>
                    </div>
                    <div className="space-y-1">
                      <p className="font-semibold text-gray-800">Credit Card</p>
                      <p className="text-sm text-gray-600 font-mono">
                        {maskedCardNumber}
                      </p>
                      <p className="text-xs text-gray-500">
                        Expires {clientCardExpirationDate}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="mt-12 border-t border-gray-200 pt-8">
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button
                  onClick={downloadReceipt}
                  className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                >
                  <Download className="h-5 w-5" />
                  <span>Download Receipt</span>
                </button>

                <button onClick={() => navigate("/")} className="flex items-center space-x-2 bg-gradient-to-r from-gray-600 to-slate-600 hover:from-gray-700 hover:to-slate-700 text-white py-3 px-6 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200">
                  <Home className="h-5 w-5" />
                  <span>Back to Dashboard</span>
                </button>
              </div>
            </div>
          </div>
        </Card>

        {/* Next Steps Card */}
        <Card className="border-0 shadow-xl backdrop-blur-xl bg-gradient-to-r from-emerald-50 to-teal-50 border border-emerald-100">
          <div className="p-8">
            <div className="flex items-center space-x-3 mb-6">
              <ArrowRight className="h-6 w-6 text-emerald-600" />
              <h3 className="text-2xl font-bold text-gray-800">What's Next?</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* <div className="text-center space-y-3">
                <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center mx-auto">
                  <Mail className="h-6 w-6 text-emerald-600" />
                </div>
                <h4 className="font-semibold text-gray-800">
                  Check Your Email
                </h4>
                <p className="text-sm text-gray-600">
                  A confirmation email with your receipt has been sent to your
                  inbox.
                </p>
              </div> */}

              <div className="text-center space-y-3">
                <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mx-auto">
                  <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs font-bold">C</span>
                  </div>
                </div>
                <h4 className="font-semibold text-gray-800">
                  Use Your Credits
                </h4>
                <p className="text-sm text-gray-600">
                  You now have{" "}
                  <span className="font-bold text-purple-600">
                    {credits} credits
                  </span>{" "}
                  ready to use in your account.
                </p>
              </div>

              <div className="text-center space-y-3">
                <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mx-auto">
                  <Calendar className="h-6 w-6 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-800">
                  {plan === "free"
                    ? "Free Access"
                    : plan === "select_24h"
                    ? "Available for 24 hours"
                    : plan === "discover_72h"
                    ? "Available for 72 hours"
                    : "Available for 96 hours"}
                </h4>
                <p className="text-sm text-gray-600">
                  {plan === "free"
                    ? "Your free access is now active."
                    : plan === "select_24h"
                    ? "Your 24-hour access is now active."
                    : plan === "discover_72h"
                    ? "Your 72-hour access is now active."
                    : "Your 96-hour access is now active."}
                </p>
              </div>
            </div>
          </div>
        </Card>

        {/* Support Section */}
        <div className="text-center space-y-4 pb-8">
          <p className="text-gray-600">
            Need help? Contact our support team at{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-purple-600 hover:text-purple-700 font-semibold"
            >
              <EMAIL>
            </a>
          </p>
          <p className="text-sm text-gray-500">
            Transaction processed securely by our payment partner
          </p>
        </div>
      </div>
    </ChatLayout>
  );
}

export default SuccessPaymentPage;
