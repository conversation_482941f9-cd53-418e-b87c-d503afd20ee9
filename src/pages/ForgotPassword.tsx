import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Button, Input,
  ThemeProvider,
  useThemeClasses,
  BackgroundWrapper
} from "../modules/shared/presentation";
import { Email } from "../modules/auth/domain/valueobjects/Email";
import { useAuth } from "../modules/auth/presentation/hooks/useAuth";

const ForgotPasswordContent: React.FC = () => {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isEmailSent, setIsEmailSent] = useState(false);
  const { forgotPassword } = useAuth();
  const themeClasses = useThemeClasses();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);
    
    try {
      const emailValue = new Email(email);
      await forgotPassword(emailValue.getValue());
      setIsEmailSent(true);
    } catch (error) {
      setError("Email not found");
    } finally {
      setIsLoading(false);
    }
  };

  if (isEmailSent) {
    return (
      <div className="flex items-center justify-center p-4 min-h-screen">
        <div className="w-full max-w-md">
          {/* Logo/Brand - Version succès */}
          <div className="text-center mb-12">
            <div className="relative inline-block mb-6">
              {/* Halo effect vert */}
              <div className="absolute inset-0 bg-gradient-to-r from-green-400/30 via-emerald-400/30 to-teal-400/30 rounded-full blur-xl scale-150 animate-pulse"></div>
              
              {/* Logo principal avec icône de succès */}
              <div className="relative bg-gradient-to-br from-green-500 via-emerald-600 to-teal-600 w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl backdrop-blur-xl border border-white/20">
                <svg className="w-10 h-10 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {/* Sparkle décoratif */}
                <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-bounce">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs">✨</span>
                  </div>
                </div>
                {/* Reflet */}
                <div className="absolute inset-2 bg-gradient-to-br from-white/30 to-transparent rounded-xl pointer-events-none"></div>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold bg-gradient-to-r from-green-500 via-emerald-400 to-pink-500 bg-clip-text text-transparent mb-2">
              Email sent !
            </h1>
            <p className="text-black/70 text-lg">
              Check your inbox
            </p>
          </div>

          {/* Card glassmorphique de succès */}
          <div className="relative group">
            {/* Effet de brillance d'arrière-plan */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 blur-xl group-hover:blur-2xl transition-all duration-500"></div>
            
            <div className="relative backdrop-blur-md border shadow-2xl overflow-hidden">
              {/* Reflet en haut */}
              <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
              
              <div className="p-8 text-center">
                <div className="mb-8">
                  <div className="relative inline-block mb-6">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-green-500/20 to-emerald-500/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-green-400/30">
                      <svg className="w-8 h-8 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-white mb-4">
                    Instructions sent
                  </h3>
                  <p className="text-white/70 mb-2 text-base">
                    We have sent a reset link to
                  </p>
                  <p className="text-green-300 font-semibold mb-4">{email}</p>
                  <p className="text-white/50 text-sm">
                    Please check your spam folder if you don't receive the email within the next few minutes.
                  </p>
                </div>

                <div className="space-y-4">
                  <Button 
                    variant="outline" 
                    className="w-full bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300"
                    onClick={() => setIsEmailSent(false)}
                  >
                    Resend email
                  </Button>
                  <Link to="/login">
                    <Button variant="ghost" className="w-full text-white/70 hover:text-white hover:bg-white/5 transition-all duration-300">
                      Back to login
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center p-4 min-h-screen">
      <div className="w-full max-w-md">
        {/* Logo/Brand - Premium glassmorphique */}
        <div className="text-center mb-12">
          <div className="relative inline-block mb-6">
            {/* Halo effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-400/30 via-indigo-400/30 to-purple-400/30 rounded-full blur-xl scale-150 animate-pulse"></div>
            
            {/* Logo principal */}
            <div className="relative bg-gradient-to-br from-purple-500 via-pink-600 to-purple-600 w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl backdrop-blur-xl border border-white/20">
              <span className="text-3xl font-bold text-white drop-shadow-lg">T</span>
              {/* Icône clé décorative */}
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-amber-400 rounded-full animate-pulse">
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs">🔑</span>
                </div>
              </div>
              {/* Reflet */}
              <div className="absolute inset-2 bg-gradient-to-br from-white/30 to-transparent rounded-xl pointer-events-none"></div>
            </div>
          </div>
          
          <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-500 via-blue-400 to-purple-500 bg-clip-text text-transparent mb-2">
            Forgot password?
          </h1>
          <p className="text-black/70 text-lg">
            No worries, we can help you recover your account
          </p>
        </div>

        {/* Card glassmorphique premium */}
        <div className="relative group">
          {/* Effet de brillance d'arrière-plan */}
          {/* <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div> */}
          
          <div className="bg-gradient-to-br from-pink-500 to-purple-600 p-8 shadow-2xl w-full max-w-md">
            {/* Reflet en haut */}
            {/* <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div> */}
            
            <div className="p-8">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-white mb-2">Reset your password</h2>
                <p className="text-white/60">
                  Enter your email address to receive a password reset link
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="relative">
                  <Input
                    label="Email address"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-blue-400/50 focus:ring-blue-400/20"
                    error={error}
                    helperText="We will send you a link to reset your password"
                    leftIcon={
                      <svg className="w-5 h-5 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    }
                    required
                  />
                </div>

                {error && (
                  <div className="bg-red-500/10 border border-red-400/30 backdrop-blur-sm rounded-xl p-4">
                    <p className="text-red-300 text-sm font-medium">{error}</p>
                  </div>
                )}

                <Button 
                  type="submit" 
                  size="lg" 
                  className="w-full bg-gradient-to-r from-black/70 to-indigo-600 text-white border-0 shadow-xl shadow-blue-500/25 hover:shadow-blue-500/40 transition-all duration-300"
                  isLoading={isLoading}
                  disabled={!email}
                >
                  Send password reset link
                </Button>
              </form>

              {/* Separator */}
              <div className="relative my-8">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-white/20" />
                </div>
              </div>
              
              <div className="text-center">
                <Link 
                  to="/login" 
                  className="text-blue-300 hover:text-blue-200 text-sm transition-colors duration-200 hover:underline flex items-center justify-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to login
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Footer moderne */}
        <div className="text-center mt-8">
          <p className="text-white/40 text-xs leading-relaxed">
            Need help?{" "}
            <Link to="/support" className="text-blue-300 hover:text-blue-200 hover:underline transition-colors">
              Contact our support
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

const ForgotPassword: React.FC = () => {
  return (
    <ThemeProvider>
      <BackgroundWrapper variant="auth">
        <ForgotPasswordContent />
      </BackgroundWrapper>
    </ThemeProvider>
  );
};

export default ForgotPassword;
