import React from "react";
import { Gem } from "lucide-react";
import { ChatLayout } from "@/components/ChatLayout";
import { useNavigate } from "react-router-dom";

const plans = [
  {
    name: "FREE ACCESS",
    plan: "free",
    price: "5 Credits",
    color: "border-green-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-green-500",
    description: (
      <>
        <p className="font-semibold">
          You can use your credits for the following:
        </p>
        <ul className="list-disc ml-5 mt-2">
          <li>1 like is 1 credit</li>
          <li>1 unblurred photo is 5 credits</li>
          <li>1 messaging contact is 15 credits</li>
        </ul>
        <p className="mt-3 text-sm text-red-500 font-semibold">
          ⚠️ Valid until your credits are exhausted.
        </p>
      </>
    ),
  },
  {
    name: "24h TO SELECT",
    plan: "select_24h",
    price: "50 Credits",
    special: true,
    color: "border-yellow-500 bg-black text-white",
    buttonColor: "bg-pink-500",
    iconColor: "text-yellow-400",
    description: (
      <>
        <p className="font-semibold">
          Content equivalent to a value of 80 credits:
        </p>
        <ul className="list-disc ml-5 mt-5">
          <li>40 likes</li>
          <li>5 unblurred photos</li>
          <li>1 messaging contact</li>
        </ul>
        <p className="text-yellow-300 mt-2">
          💡 Save 30 credits by choosing this pack.
        </p>
      </>
    ),
  },
  {
    name: "72h TO DISCOVER",
    plan: "discover_72h",
    price: "150 Credits",
    color: "border-blue-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-blue-500",
    description: (
      <>
        <p className="font-semibold">
          Content equivalent to a value of 225 credits:
        </p>
        <ul className="list-disc ml-5 mt-5">
          <li>120 likes</li>
          <li>12 unblurred photos</li>
          <li>3 messaging contacts</li>
        </ul>
        <p className="text-blue-600 mt-2">💡 Save 75 credits with this pack.</p>
      </>
    ),
  },
  {
    name: "96h TO CONCLUDE",
    plan: "conclude_96h",
    price: "200 Credits",
    color: "border-pink-500 bg-white text-black",
    buttonColor: "bg-pink-500",
    iconColor: "text-pink-500",
    description: (
      <>
        <p className="font-semibold">
          Content equivalent to a value of 300 credits:
        </p>
        <ul className="list-disc ml-5 mt-5">
          <li>150 likes</li>
          <li>15 unblurred photos</li>
          <li>5 messaging contacts</li>
        </ul>
        <p className="text-pink-600 mt-2">
          💡 Save 100 credits by opting for this complete pack.
        </p>
      </>
    ),
  },
];

const PricingPage = () => {
  const navigate = useNavigate();
  const handleChoosePlan = (plan: string, customAmountInEuro?: number) => {
    let price = 1;

    switch (plan) {
      case "free":
        // Si l'utilisateur choisit un montant, on l'utilise
        if (!customAmountInEuro || customAmountInEuro < 1) {
          console.warn("Montant personnalisé requis pour le plan free");
          return;
        }
        price = customAmountInEuro;
        break;

      case "select_24h":
        price = 50 / 5; // 10 €
        break;

      case "discover_72h":
        price = 150 / 5; // 30 €
        break;

      case "conclude_96h":
        price = 200 / 5; // 40 €
        break;

      default:
        console.warn("Plan inconnu");
        return;
    }

    console.log("Selected plan:", plan);
    console.log("Price (€):", price);

    navigate("/payment-checkout", {
      state: { plan, price },
    });
  };

  return (
    <ChatLayout className="flex flex-col mt-12 lg:mt-0 min-h-screen bg-gray-50">
      <div className="p-8 max-w-7xl mx-auto">
        <div className="mb-8 bg-white backdrop-blur-md shadow-lg rounded-xl p-6">
          <h1 className="text-3xl font-bold text-black text-center mb-4">
            Amazing toodiscreet features you can't live without.
          </h1>
          
          <p className="text-lg text-black text-center w-full lg:w-3/4 mx-auto mb-4">
            Have <span className="text-pink-500">5 crédits minimum</span> For accessing to your <span className="text-pink-500">like</span>, <span className="text-pink-500">unblurred photos</span>
             & <span className="text-pink-500">messaging contacts</span>. And more to <span className="text-pink-500">meet more people</span>.
          </p>
          
        
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.name}
              className={`border relative rounded-xl p-6 flex flex-col justify-between ${plan.color} shadow-lg hover:shadow-xl transition-shadow`}
            >
              {plan.special && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 bg-pink-600 text-white rounded-full px-3 py-1 mb-4 w-fit shadow font-semibold text-sm z-10">
                  Special
                </div>
              )}
              <div className="flex flex-row items-center justify-center mb-4">
                <Gem className={`w-10 h-10 ${plan.iconColor}`} />
                <div className="flex flex-col gap-1 ml-4">
                  <h2 className="text-xl font-semibold">{plan.name}</h2>
                  <p>Package</p>
                </div>
              </div>
              <p className="text-2xl text-center font-bold mt-2">
                {plan.price}
              </p>
              <div className="w-full h-[1px] mt-5 bg-gray-200 opacity-30"></div>
              <div className="mt-4 text-sm flex-grow">{plan.description}</div>
              <button
                onClick={() => handleChoosePlan(plan.plan, 1)}
                className={`mt-6 ${plan.buttonColor} text-white py-3 px-6 rounded-lg font-semibold hover:opacity-90 transition-opacity`}
              >
                Choose Plan
              </button>
            </div>
          ))}
        </div>
      </div>
    </ChatLayout>
  );
};

export default PricingPage;
