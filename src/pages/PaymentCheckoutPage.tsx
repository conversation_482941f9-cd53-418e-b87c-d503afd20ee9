import { ChatLayout } from "@/components/ChatLayout";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import packApiRepository, {
  PackApiRepository,
} from "@/modules/packs/infrastructure/api/PackApiRepository";
import { Card } from "@/modules/shared";
import {
  EuroIcon,
  CreditCard,
  Mail,
  User,
  Calendar,
  Shield,
} from "lucide-react";
import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

function PaymentCheckoutPage() {
  const { state } = useLocation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { plan, price } = state;
  const [customAmount, setCustomAmount] = React.useState(price);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = React.useState(false);
  const [paymentData, setPaymentData] = React.useState({
    clientName: "",
    clientEmail: "",
    customAmount,
    clientCardNumber: "",
    clientCardExpirationDate: "",
    clientCardCvv: "",
    plan,
    price,
    credits: 0,
  });

  const [confirmPayment, setConfirmPayment] = React.useState(false);

  console.log("Plan:", plan);
  console.log("Price:", price);

  // Update custom amount in payment data when it changes
  React.useEffect(() => {
    setPaymentData((prev) => ({ ...prev, customAmount }));
  }, [customAmount]);

  const handlePayment = async () => {
    setIsLoading(true);
    try {
      if (plan === "free") {
        paymentData.credits = customAmount * 5;
      } else if (plan === "select_24h") {
        paymentData.credits = 50;
      } else if (plan === "discover_72h") {
        paymentData.credits = 150;
      } else if (plan === "conclude_96h") {
        paymentData.credits = 200;
      }
      // Handle payment logic here
      console.log("Payment data:", paymentData);
      const data = {
        user_id: user?.id,
        pack_name: plan,
        description: "Pack de test",
        price: plan === "free" ? customAmount : paymentData.price,
        credit_value: paymentData.credits,
      };
      // Simulate payment processing
      await new Promise((resolve) => setTimeout(resolve, 2000));
      // IMplement create pack ici
      await packApiRepository.createPack(data);
      console.log("Payment successful");
      setIsLoading(false);
      setConfirmPayment(false);

      navigate("/success-payment", { state: paymentData });
    } catch (error) {
      console.error("Error during payment:", error);
      toast.error("Error during payment");
      setIsLoading(false);
    }
  };

  const handleOpenConfirmPayment = async () => {
    setConfirmPayment(true);
  };

  const isValidCardNumber = (cardNumber: string) => {
    // Supprimer les espaces pour la validation
    const cleanNumber = cardNumber.replace(/\s/g, "");
    // Vérifier que c'est exactement 16 chiffres
    return /^\d{16}$/.test(cleanNumber);
  };

  const isFormValid = () => {
    return (
      paymentData.clientName.trim() !== "" &&
      paymentData.clientEmail.trim() !== "" &&
      isValidCardNumber(paymentData.clientCardNumber) && // Validation du numéro de carte
      paymentData.clientCardExpirationDate.trim() !== "" &&
      paymentData.clientCardCvv.trim() !== "" &&
      isTermsAccepted
    );
  };

  const formatCardNumber = (value: string) => {
    // Supprimer tous les caractères non numériques
    const numericValue = value.replace(/\D/g, "");

    // Limiter à 16 chiffres maximum
    const limitedValue = numericValue.slice(0, 16);

    // Ajouter un espace tous les 4 chiffres
    return limitedValue.replace(/(\d{4})(?=\d)/g, "$1 ");
  };

  return (
    <>
      {/* Modale confimation payment */}
      {confirmPayment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white/100 p-8 rounded-2xl shadow-2xl w-[90vw] max-w-md">
            <h2 className="text-2xl font-bold mb-4 text-gray-900">
              Confirm Payment
            </h2>
            <p className="text-lg text-gray-700 mb-6">
              Please confirm your payment details before proceeding.
            </p>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">Plan</span>
                <span className="font-semibold capitalize text-gray-800">
                  {plan === "free"
                    ? "FREE ACCESS"
                    : plan === "select_24h"
                    ? "24h TO SELECT"
                    : plan === "discover_72h"
                    ? "72h TO DISCOVER"
                    : "96h TO CONCLUDE"}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Credits</span>
                <span className="font-semibold text-gray-800">
                  {plan === "free" ? customAmount * 5 : price * 5} credits
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Amount</span>
                <span className="font-semibold text-gray-800">
                  {plan === "free" ? customAmount : price} €
                </span>
              </div>
            </div>
            <div className="flex justify-end mt-6">
              <button
                onClick={() => setConfirmPayment(false)}
                className="px-4 py-2 rounded-lg bg-gray-200 text-gray-700 mr-4"
              >
                Cancel
              </button>
              <button
                onClick={handlePayment}
                className="px-4 py-2 rounded-lg bg-purple-600 text-white"
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white/30 border-t-white"></div>
                    <span className="text-xl text-white">Processing...</span>
                  </div>
                ) : (
                  <span className="flex items-center gap-2">
                    Confirm Payment
                  </span>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      <ChatLayout className="p-6 min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex mt-12 lg:mt-0 items-center justify-center">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-pink-600 bg-clip-text text-transparent flex items-center space-x-4">
              <EuroIcon className="h-10 w-10 text-pink-500" />
              <span>Payment Checkout</span>
            </h2>
          </div>

          {/* Main Card */}
          <Card className="border-0 shadow-2xl backdrop-blur-xl bg-white/80 overflow-hidden">
            <div className="p-8 lg:p-12">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Plan Information */}
                <div className="space-y-6">
                  <div className="p-6 bg-gradient-to-br from-purple-50 to-blue-50 rounded-2xl border border-purple-100">
                    <h3 className="text-2xl font-bold text-gray-800 mb-2">
                      Selected Plan
                    </h3>
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-semibold text-purple-600 capitalize">
                        {plan === "free"
                          ? "FREE ACCESS"
                          : plan === "select_24h"
                          ? "24h TO SELECT"
                          : plan === "discover_72h"
                          ? "72h TO DISCOVER"
                          : "96h TO CONCLUDE"}
                      </span>
                      <span className="text-2xl font-bold text-gray-800">
                        {price} €
                      </span>
                    </div>
                  </div>

                  {/* Custom Amount for Free Plan */}
                  {plan === "free" && (
                    <div className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-100">
                      <label
                        htmlFor="customAmount"
                        className="block text-lg font-semibold text-gray-800 mb-3"
                      >
                        Custom Amount (€)
                      </label>
                      <div className="relative">
                        <EuroIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                        <input
                          type="number"
                          id="customAmount"
                          value={customAmount}
                          onChange={(e) =>
                            setCustomAmount(Number(e.target.value))
                          }
                          name="customAmount"
                          min="1"
                          className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 text-lg font-medium bg-white/80 backdrop-blur-sm transition-all duration-200"
                          placeholder="Enter amount"
                        />
                      </div>
                    </div>
                  )}

                  {/* Payment Summary */}
                  <div className="p-6 bg-gradient-to-br from-gray-50 to-slate-50 rounded-2xl border border-gray-100">
                    <h4 className="text-lg font-semibold text-gray-800 mb-3">
                      Payment Summary
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Plan</span>
                        <span className="font-semibold capitalize text-gray-800">
                          {plan === "free"
                            ? "FREE ACCESS"
                            : plan === "select_24h"
                            ? "24h TO SELECT"
                            : plan === "discover_72h"
                            ? "72h TO DISCOVER"
                            : "96h TO CONCLUDE"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Amount</span>
                        <span className="font-semibold text-gray-800">
                          {plan === "free" ? customAmount : price} €
                        </span>
                      </div>
                      <hr className="my-2" />
                      <div className="flex justify-between text-lg font-bold">
                        <span>Total</span>
                        <span className="text-purple-600">
                          {plan === "free" ? customAmount : price} €
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Form */}
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-gray-800 mb-6">
                    Payment Details
                  </h3>

                  {/* Personal Information */}
                  <div className="space-y-4">
                    <div className="relative">
                      <label
                        htmlFor="clientName"
                        className="block text-sm font-semibold text-gray-700 mb-2"
                      >
                        Full Name
                      </label>
                      <div className="relative">
                        <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                        <input
                          type="text"
                          id="clientName"
                          value={paymentData.clientName}
                          onChange={(e) =>
                            setPaymentData({
                              ...paymentData,
                              clientName: e.target.value,
                            })
                          }
                          name="clientName"
                          className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 bg-white/80 backdrop-blur-sm transition-all duration-200"
                          placeholder="Enter your full name"
                        />
                      </div>
                    </div>

                    <div className="relative">
                      <label
                        htmlFor="clientEmail"
                        className="block text-sm font-semibold text-gray-700 mb-2"
                      >
                        Email Address
                      </label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                        <input
                          type="email"
                          id="clientEmail"
                          value={paymentData.clientEmail}
                          onChange={(e) =>
                            setPaymentData({
                              ...paymentData,
                              clientEmail: e.target.value,
                            })
                          }
                          name="clientEmail"
                          className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 bg-white/80 backdrop-blur-sm transition-all duration-200"
                          placeholder="Enter your email"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Card Information */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                      <CreditCard className="h-5 w-5" />
                      <span>Card Information</span>
                    </h4>

                    {/* Card Number */}
                    <div className="relative">
                      <label
                        htmlFor="clientCardNumber"
                        className="block text-sm font-semibold text-gray-700 mb-2"
                      >
                        Card Number
                      </label>
                      <div className="relative">
                        <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                        <input
                          type="text"
                          id="clientCardNumber"
                          value={paymentData.clientCardNumber}
                          onChange={(e) => {
                            const formattedValue = formatCardNumber(
                              e.target.value
                            );
                            setPaymentData({
                              ...paymentData,
                              clientCardNumber: formattedValue,
                            });
                          }}
                          name="clientCardNumber"
                          className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 bg-white/80 backdrop-blur-sm transition-all duration-200"
                          placeholder="1234 5678 9012 3456"
                          maxLength={19} // 16 chiffres + 3 espaces
                        />
                      </div>
                    </div>

                    {/* Expiry Date and CVV */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="relative">
                        <label
                          htmlFor="clientCardExpirationDate"
                          className="block text-sm font-semibold text-gray-700 mb-2"
                        >
                          Expiry Date
                        </label>
                        <div className="relative">
                          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                          <input
                            type="text"
                            id="clientCardExpirationDate"
                            value={paymentData.clientCardExpirationDate}
                            onChange={(e) =>
                              setPaymentData({
                                ...paymentData,
                                clientCardExpirationDate: e.target.value,
                              })
                            }
                            name="clientCardExpirationDate"
                            className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 bg-white/80 backdrop-blur-sm transition-all duration-200"
                            placeholder="MM/YY"
                            maxLength={5}
                          />
                        </div>
                      </div>

                      <div className="relative">
                        <label
                          htmlFor="clientCardCvv"
                          className="block text-sm font-semibold text-gray-700 mb-2"
                        >
                          CVV
                        </label>
                        <div className="relative">
                          <Shield className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10" />
                          <input
                            type="text"
                            id="clientCardCvv"
                            value={paymentData.clientCardCvv}
                            onChange={(e) =>
                              setPaymentData({
                                ...paymentData,
                                clientCardCvv: e.target.value,
                              })
                            }
                            name="clientCardCvv"
                            className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-gray-800 bg-white/80 backdrop-blur-sm transition-all duration-200"
                            placeholder="123"
                            maxLength={3}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Terms and Conditions */}
                  <div className="space-y-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={isTermsAccepted}
                        onChange={(e) => setIsTermsAccepted(e.target.checked)}
                        id="termsAndConditions"
                        name="termsAndConditions"
                        className="h-6 w-6 text-purple-600 border-2 border-gray-500 rounded"
                      />
                      <label
                        htmlFor="termsAndConditions"
                        className="ml-2 text-xl text-gray-700 font-semibold"
                      >
                        I agree to the{" "}
                        <a
                          href="/term-of-use"
                          target="_blank"
                          className="text-purple-600 hover:underline"
                        >
                          Terms and Conditions
                        </a>
                        .
                      </label>
                    </div>
                  </div>

                  {/* Pay Button */}
                  <button
                    disabled={!isFormValid() || isLoading}
                    onClick={handleOpenConfirmPayment}
                    className={`w-full flex justify-center items-center ${
                      isFormValid() && !isLoading
                        ? "bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 hover:from-purple-700 hover:via-pink-700 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transform hover:scale-[1.02] transition-all duration-200"
                        : "bg-gray-400 text-gray-600 cursor-not-allowed shadow-none"
                    } py-4 px-8 rounded-xl text-lg font-semibold mt-8`}
                    style={
                      !isFormValid() || isLoading
                        ? {
                            backgroundColor: "#9CA3AF",
                            color: "#4B5563",
                            opacity: 0.7,
                            cursor: "not-allowed",
                          }
                        : {}
                    }
                  >
                    <span className="flex items-center gap-2">
                      Pay {plan === "free" ? customAmount : price} € Now
                    </span>
                  </button>

                  {/* Security Notice */}
                  <div className="flex items-center justify-center space-x-2 text-sm text-gray-500 mt-4">
                    <Shield className="h-4 w-4" />
                    <span>
                      Your payment information is secure and encrypted
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </ChatLayout>
    </>
  );
}

export default PaymentCheckoutPage;
