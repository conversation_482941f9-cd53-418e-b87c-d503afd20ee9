import AuthNav from "@/components/AuthNav";
import { Card } from "@/modules/shared";
import { Home, Mail, MapPin } from "lucide-react";
import React from "react";
import { useNavigate } from "react-router-dom";

const rules = [
  {
    id: 1,
    description:
      "Respect others: Treat others with respect and kindness, even if you don't feel a romantic connection. Don't use offensive language or engage in inappropriate behavior.",
  },
  {
    id: 2,
    description:
      "Be honest: Be honest about your intentions and interests on your profile. Don't misrepresent yourself or your intentions to others.",
  },
  {
    id: 3,
    description:
      "Stay safe: Take safety precautions when meeting someone in person. Meet in a public place and let someone know where you'll be. Don't give out personal information such as your address or phone number until you feel comfortable.",
  },
  {
    id: 4,
    description:
      "Report inappropriate behavior: If you encounter any inappropriate behavior or suspicious activity, report it immediately to the Amorzee team. The app has a reporting feature that allows you to report any issues or concerns.",
  },
  {
    id: 5,
    description:
      "Use appropriate language: Avoid using explicit or inappropriate language on the app. Keep your messages and profile clean and respectful.",
  },
  {
    id: 6,
    description:
      "Don't spam: Don't spam other users with messages or requests. This can be annoying and may result in your account being blocked.",
  },
  {
    id: 7,
    description:
      "Be patient: Finding the right connection takes time. Don't get discouraged if you don't find the right match right away. Keep using the app and stay positive.",
  },
];

export default function Guidelines() {
  const navigate = useNavigate();
  return (
    <div className="relative min-h-screen">
      <AuthNav />
      <div className="absolute inset-0 bg-gradient-to-tr from-purple-600/10 via-red-500/10 to-pink-500/10 -z-10"></div>
      <div className="max-w-7xl mx-auto flex py-12 px-6">
        <Card className="w-full lg:w-4/5 bg-white backdrop-blur-xl shadow-2xl p-6 mx-auto">
          <div className="flex flex-col  space-y-6">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-red-400 to-pink-500 bg-clip-text text-transparent flex items-center space-x-3">
              <span>Guidelines</span>
            </h2>
            <p>Here are some guidelines for using Amorzee dating app:</p>

            {rules.map((rule) => (
              <div key={rule.id} className="flex flex-row space-x-2">
                <p className="font-bold">{rule.id}.</p>
                <p className="text-gray-600">{rule.description}</p>
              </div>
            ))}

            <p>
              Overall, the key to a successful and enjoyable experience on
              Amorzee dating app is to treat others with respect, be honest, and
              stay safe.
            </p>
          </div>
        </Card>
      </div>
      {/* Footer */}
      <div className="w-full  border-t border-gray-200 border-b border-gray-200 bg-white py-6 ">
        <div className="flex max-w-7xl mx-auto flex-col sm:flex-row justify-between items-center">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <button
              onClick={() => navigate("/term-of-use")}
              className="hover:underline"
            >
              Terms & Conditions
            </button>
            <button
              onClick={() => navigate("/privacy-and-policy")}
              className="hover:underline"
            >
              Privacy Policy
            </button>
            <button
              onClick={() => navigate("/contacts")}
              className="hover:underline"
            >
              Contact
            </button>
            <button
              onClick={() => navigate("/guidelines")}
              className="hover:underline"
            >
              Guidelines
            </button>
          </div>
          <div className="text-center text-sm text-gray-500 mt-4 sm:mt-0">
            <p>Copyright © 2025 TooDiscreet. All rights reserved.</p>
          </div>
        </div>
      </div>
      {/* Address */}
      <div className="w-full  border-t border-gray-200 bg-white py-6 ">
        <div className="flex max-w-7xl mx-auto flex-col space-y-2">
          <div className="flex items-center space-x-2">
            <Home size={16} />
            <p>Keytech Partners SIA</p>
          </div>
          <div className="flex items-center space-x-2">
            <Mail size={16} />
            <span>Registration number 40203645640</span>
          </div>
          <div className="flex items-center space-x-2">
            <MapPin size={16} />
            <span> 7, Tapesu iela, LV-1083, Riga, Latvia</span>
          </div>
        </div>
      </div>
      {/* Button floating scroll to top */}
      
    </div>
  );
}
