import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  Input,
  Button,
  ThemeProvider,
  useThemeClasses,
  BackgroundWrapper,
} from "@/modules/shared/presentation";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import AuthNav from "@/components/AuthNav";
import { Heart } from "lucide-react";

const LoginPageContent: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const themeClasses = useThemeClasses();

  // Rediriger vers l'accueil si l'utilisateur est déjà connecté
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/");
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    // Validation côté client
    if (!email.trim()) {
      setError("Email requis");
      setIsLoading(false);
      return;
    }

    if (!password.trim()) {
      setError("Mot de passe requis");
      setIsLoading(false);
      return;
    }

    try {
      await login(email, password);
      navigate("/");
    } catch (error) {
      setError("Email ou mot de passe incorrect");
    } finally {
      setIsLoading(false);
    }
  };

  const profileImages = [
    "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1024311/pexels-photo-1024311.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1065084/pexels-photo-1065084.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1181690/pexels-photo-1181690.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1212984/pexels-photo-1212984.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1310522/pexels-photo-1310522.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1300402/pexels-photo-1300402.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1382734/pexels-photo-1382734.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1484794/pexels-photo-1484794.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1542085/pexels-photo-1542085.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1674752/pexels-photo-1674752.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1680172/pexels-photo-1680172.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1758144/pexels-photo-1758144.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1844547/pexels-photo-1844547.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/1933873/pexels-photo-1933873.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2381069/pexels-photo-2381069.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2709388/pexels-photo-2709388.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2726111/pexels-photo-2726111.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2787341/pexels-photo-2787341.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/2888150/pexels-photo-2888150.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/3094215/pexels-photo-3094215.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/3211476/pexels-photo-3211476.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
    "https://images.pexels.com/photos/3307758/pexels-photo-3307758.jpeg?auto=compress&cs=tinysrgb&w=200&h=200&dpr=1",
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      <div className="absolute inset-0 grid grid-cols-8 md:grid-cols-12 lg:grid-cols-16 gap-2 p-4">
        {Array.from({ length: 96 }, (_, index) => (
          <div
            key={index}
            className="aspect-square rounded-lg overflow-hidden opacity-10 hover:opacity-20 transition-opacity duration-300"
          >
            <img
              src={profileImages[index % profileImages.length]}
              alt={`Profile ${index + 1}`}
              className="w-full h-full object-cover"
            />
          </div>
        ))}
      </div>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/60 via-white/30 to-white/10 backdrop-blur-sm"></div>
      <div className="relative flex items-center justify-between w-full z-50 flex p-0 m-0 rounded-none">
        {/* Partie gauche : logo + nom sur fond blanc */}
        <div className="flex items-center px-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 border border-pink-500 rounded-full flex items-center justify-center">
              <Heart className="text-pink-500" size={16} />
            </div>
            <span className="text-xl font-bold text-purple-700">
              TooDiscreet
            </span>
          </div>
        </div>

        {/* Partie qui prend l'espace vide */}

        {/* Partie droite : boutons sur fond rose */}
        <div className="flex w-1/2 sm:w-1/3 md:w-1/4 flex justify-end items-center space-x-8 h-16 px-4">
          <button
            type="button"
            onClick={() => navigate("/login")}
            className="text-black px-4 py-2 rounded-full hover:bg-pink-600 hover:shadow-lg"
          >
            Login
          </button>
          <button type="button">
            <div
              onClick={() => navigate("/signup")}
              className="bg-white text-black rounded-full px-4 py-2 hover:bg-pink-600 hover:text-white hover:shadow-lg"
            >
              Register
            </div>
          </button>
        </div>
      </div>
      <div className="relative z-40 flex items-center justify-center min-h-[calc(100vh-4rem)] p-4">
        <div className="bg-gradient-to-br from-pink-500 to-purple-600 p-8 shadow-2xl w-full max-w-md">
          <div className="text-left mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              Welcome back,
            </h1>
            <p className="text-white/80">Login to your account to continue.</p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-5">
              <div className="relative">
                <Input
                  label="Email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="bg-white/5 border-white/20 text-white rounded-none placeholder:text-white/50 focus:border-blue-400/50 focus:ring-blue-400/20"
                  leftIcon={
                    <svg
                      className="w-5 h-5 text-white/70"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                      />
                    </svg>
                  }
                  required
                />
              </div>

              <div className="relative">
                <Input
                  label="Password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="••••••••"
                  className="bg-white/5 border-white/20 rounded-none text-white placeholder:text-white/50 focus:border-blue-400/50 focus:ring-blue-400/20"
                  leftIcon={
                    <svg
                      className="w-5 h-5 text-white/70"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                      />
                    </svg>
                  }
                  required
                />
              </div>
            </div>

            {error && (
              <div className="bg-red-500/10 border border-red-400/30 backdrop-blur-sm rounded-xl p-4">
                <p className="text-red-300 text-sm font-medium">{error}</p>
              </div>
            )}

            <div className="flex flex-wrap justify-between items-center">
              <button
                type="submit"
                className="w-full md:w-1/4 !bg-black/20 !rounded-full py-3 px-4 text-white hover:text-white border-0 shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isLoading}
              >
                {isLoading ? "Loading..." : "Login"}
              </button>
              <div className="text-right">
                <Link
                  to="/forgot-password"
                  className="text-white/70 hover:text-white text-md transition-colors duration-20"
                >
                  Forgot password?
                </Link>
              </div>
            </div>
          </form>

          {/* Séparateur élégant */}
          <div className="relative my-8">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t border-white/20" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-white/5 backdrop-blur-sm px-4 py-1 rounded-full text-white/60 border border-white/10">
                or
              </span>
            </div>
          </div>

          <div className="text-center">
            <p className="text-white/60 text-sm">
              Don't have an account yet?{" "}
              <Link
                to="/signup"
                className="font-medium text-purple-300 hover:text-purple-200 transition-colors duration-200 hover:underline"
              >
                Create an account
              </Link>
            </p>
          </div>

          {/* Footer moderne */}
          <div className="text-center mt-8">
            <p className="text-white/40 text-xs leading-relaxed">
              By logging in, you agree to our{" "}
              <Link
                to="/terms"
                className="text-blue-300 hover:text-blue-200 hover:underline transition-colors"
              >
                Terms of Use
              </Link>{" "}
              and our{" "}
              <Link
                to="/privacy"
                className="text-blue-300 hover:text-blue-200 hover:underline transition-colors"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

const LoginPage: React.FC = () => {
  return (
    <ThemeProvider>
      <BackgroundWrapper variant="auth">
        <LoginPageContent />
      </BackgroundWrapper>
    </ThemeProvider>
  );
};

export default LoginPage;