import React, { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import {
  Button,
  Input,
  ThemeProvider,
  useThemeClasses,
  BackgroundWrapper,
} from "@/modules/shared/presentation";
import { InvalidEmailException } from "@/modules/auth/domain/exceptions/InvalidEmail";
import { InvalidPasswordException } from "@/modules/auth/domain/exceptions/InvalidPassword";
import { InvalidUsernameException } from "@/modules/auth/domain/exceptions/InvalidUsername";
import { PasswordTooShortException } from "@/modules/auth/domain/exceptions/PasswordTooShort";
import { Email } from "@/modules/auth/domain/valueobjects/Email";
import { Password } from "@/modules/auth/domain/valueobjects/Password";
import { Username } from "@/modules/auth/domain/valueobjects/Username";
import { useAuth } from "@/modules/auth/presentation/hooks/useAuth";
import { Heart } from "lucide-react";

const SignupPageContent: React.FC = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [username, setUsername] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { signup, isAuthenticated } = useAuth();
  const themeClasses = useThemeClasses();

  const location = useLocation();
  const form = location.state;
  console.log("form: ", form);

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/onboarding");
    }
  }, [isAuthenticated, navigate]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      setIsLoading(false);
      return;
    }

    try {
      const emailValue = new Email(email);
      const passwordValue = new Password(password);
      const usernameValue = new Username(username);

      await signup(
        emailValue.getValue(),
        passwordValue.getValue(),
        usernameValue.getValue()
      );
      navigate("/onboarding", { state: {form} });
    } catch (error) {
      if (error instanceof InvalidUsernameException) {
        setError("Username invalide");
      } else if (error instanceof InvalidEmailException) {
        setError("Email address invalide");
      } else if (error instanceof PasswordTooShortException) {
        setError("Password must be at least 8 characters long");
      } else if (error instanceof InvalidPasswordException) {
        setError(
          "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
        );
      } else {
        setError("An error occurred, username or email already taken, please try again");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const passwordStrength = () => {
    if (!password) return 0;
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
  };

  const getStrengthColor = () => {
    const strength = passwordStrength();
    if (strength <= 2) return "from-red-500 to-red-600";
    if (strength === 3) return "from-yellow-500 to-orange-500";
    if (strength === 4) return "from-blue-500 to-indigo-500";
    return "from-green-500 to-emerald-500";
  };

  const getStrengthText = () => {
    const strength = passwordStrength();
    if (strength <= 2) return "Faible";
    if (strength === 3) return "Moyen";
    if (strength === 4) return "Fort";
    return "Strong";
  };

  const getStrengthTextColor = () => {
    const strength = passwordStrength();
    if (strength <= 2) return "text-red-300";
    if (strength === 3) return "text-yellow-300";
    if (strength === 4) return "text-blue-300";
    return "text-green-300";
  };

  return (
    <>
      <nav className="w-full bg-white shadow-sm border-b border-pink-100 rounded-none p-0 m-0 ">
        <div className="relative flex items-center w-full md:w-[80%] mx-auto justify-between w-full z-50 flex">
          {/* Partie gauche : logo + nom sur fond blanc */}
          <div className="flex items-center px-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 border border-pink-500 rounded-full flex items-center justify-center">
                <Heart className="text-pink-500" size={16} />
              </div>
              <span className="text-xl font-bold text-purple-700">
                TooDiscreet
              </span>
            </div>
          </div>

          {/* Partie qui prend l'espace vide */}

          {/* Partie droite : boutons sur fond rose */}
          <div className="flex w-1/2 sm:w-1/3 md:w-1/4 flex justify-end items-center space-x-8 h-16 px-4">
            <p className="hidden lg:block text-black">Already have an account?</p>
            <Link
              to="/login"
              className="text-black px-4 py-2 rounded-full hover:shadow-sm"
            >
              Login
            </Link>
          </div>
        </div>
      </nav>
      <div className="flex flex-col items-center justify-center bg-white min-h-screen m-0 p-0">
        <div className="w-full max-w-md">
          {/* Logo/Brand - Premium glassmorphique */}

          {/* Card glassmorphique premium */}
          <div className="relative group">
            {/* Effet de brillance d'arrière-plan */}
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 via-pink-500/10 to-rose-500/10 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-500"></div>

            <div className="relative bg-pink-500 backdrop-blur-xl rounded-xl border border-white/20 shadow-2xl overflow-hidden">
              {/* Reflet en haut */}

              <div className="p-8">
                <div className="text-center mb-8">
                  <h1 className="text-4xl font-bold text-white mb-2">
                    Get started,
                  </h1>
                  <p className="text-white/90 text-lg">
                    Sign up to get started finding your partner!
                  </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-5">
                  <div className="flex flex-row space-x-4">
                    <div className="relative">
                      <Input
                        label="Username"
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="Your username"
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400/50 focus:ring-purple-400/20"
                        helperText="At least 3 characters"
                        leftIcon={
                          <svg
                            className="w-5 h-5 text-white/70"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                            />
                          </svg>
                        }
                        required
                      />
                    </div>

                    <div className="relative">
                      <Input
                        label="Email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400/50 focus:ring-purple-400/20"
                        leftIcon={
                          <svg
                            className="w-5 h-5 text-white/70"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                            />
                          </svg>
                        }
                        required
                      />
                    </div>
                  </div>

                  <div className="flex flex-row space-x-4">
                    <div>
                      <div className="relative">
                        <Input
                          label="Password"
                          type="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder="••••••••"
                          className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400/50 focus:ring-purple-400/20"
                          leftIcon={
                            <svg
                              className="w-5 h-5 text-white/70"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                              />
                            </svg>
                          }
                          required
                        />
                      </div>
                    </div>

                    <div className="relative">
                      <Input
                        label="Confirm Password"
                        type="password"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="••••••••"
                        className="bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-purple-400/50 focus:ring-purple-400/20"
                        error={
                          confirmPassword && password !== confirmPassword
                            ? "Passwords do not match"
                            : undefined
                        }
                        leftIcon={
                          <svg
                            className="w-5 h-5 text-white/70"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                        }
                        required
                      />
                    </div>
                  </div>

                  <div>
                    {password && (
                      <div className="mt-3 p-3 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                        <div className="flex items-center justify-between text-xs mb-2">
                          <span className="text-white/70">
                            Password strength
                          </span>
                          <span
                            className={`font-medium ${getStrengthTextColor()}`}
                          >
                            {getStrengthText()}
                          </span>
                        </div>
                        <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                          <div
                            className={`h-full bg-gradient-to-r ${getStrengthColor()} transition-all duration-500 rounded-full`}
                            style={{
                              width: `${(passwordStrength() / 5) * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>

                  {error && (
                    <div className="bg-red-500/10 border border-red-700 backdrop-blur-sm rounded-xl p-4">
                      <p className="text-white text-sm font-medium">
                        {error}
                      </p>
                    </div>
                  )}

                  <div className="space-y-4 pt-2">
                    <Button
                      type="submit"
                      size="lg"
                      className="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white border-0 shadow-xl shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-300"
                      isLoading={isLoading}
                      disabled={
                        !email || !password || !confirmPassword || !username
                      }
                    >
                      Create Account
                    </Button>
                  </div>
                </form>

                {/* Elegant separator */}
                <div className="relative my-8">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-white/20" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white/5 backdrop-blur-sm px-4 py-1 rounded-full text-white/60 border border-white/10">
                      or
                    </span>
                  </div>
                </div>

                <div className="text-center">
                  <p className="text-white/60 text-sm">
                    Already have an account?{" "}
                    <Link
                      to="/login"
                      className="font-medium text-pink-300 hover:text-pink-200 transition-colors duration-200 hover:underline"
                    >
                      Log in
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer moderne */}
          <div className="text-center mt-8">
            <p className="text-black/40 text-xs leading-relaxed">
              By creating an account, you agree to our{" "}
              <Link
                to="/terms"
                className="text-purple-300 hover:text-purple-200 hover:underline transition-colors"
              >
                Terms of Service
              </Link>{" "}
              and our{" "}
              <Link
                to="/privacy"
                className="text-purple-300 hover:text-purple-200 hover:underline transition-colors"
              >
                Privacy Policy
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

const SignupPage: React.FC = () => {
  return (
    <ThemeProvider>
      <BackgroundWrapper variant="auth">
        <SignupPageContent />
      </BackgroundWrapper>
    </ThemeProvider>
  );
};

export default SignupPage;
