{"name": "amorzee", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-pdf/renderer": "^4.3.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "lucide-react": "^0.514.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "6.22.3", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.1", "typescript": "^4.4.2", "vaul": "^0.9.9", "web-vitals": "^2.1.0", "zod": "^3.23.8"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "test:watch": "craco test --watch", "test:coverage": "craco test --coverage --watchAll=false", "test:chat": "craco test --testPathPattern=chat --watchAll=false", "test:chat:watch": "craco test --testPathPattern=chat --watch", "test:users": "craco test --testPathPattern=users --watchAll=false", "test:users:watch": "craco test --testPathPattern=users --watch", "test:integration": "craco test --testPathPattern=integration --watchAll=false", "test:unit": "craco test --testPathPattern='(application|valueobjects|entities)' --watchAll=false", "test:all": "npm run test:unit && npm run test:integration", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@craco/craco": "^7.1.0", "@shadcn/ui": "^0.0.4", "autoprefixer": "^10.4.21", "babel-jest": "^30.0.0", "postcss": "^8.5.4", "tailwindcss": "3.4.17", "tailwindcss-animate": "^1.0.7", "ts-mockito": "^2.6.1"}}